# Day07 Java集合进阶与Map接口总结

## 📚 学习目标

通过day07_Collection_Map的学习，掌握Java集合框架的进阶特性：
1. **可变参数** - 方法参数的灵活处理
2. **Collections工具类** - 集合操作的实用工具
3. **Map接口** - 键值对数据结构的核心
4. **Map遍历** - 多种遍历方式的掌握
5. **实际应用** - 斗地主发牌案例

## 🔧 可变参数 - 方法参数的灵活处理

### 核心概念
- 可变参数允许方法接收不定数量的参数
- 语法：`数据类型... 参数名`
- 本质：可变参数在方法内部就是一个数组
- 限制：一个方法中只能有一个可变参数，且必须是最后一个参数

### 1. 可变参数基础用法 (Test)

```java
public class Test {
    public static void main(String[] args) {
        // 可变参数的调用方式
        sum(); // 不传参数
        sum(10, 20); // 传递2个参数
        sum(10, 20, 30); // 传递3个参数
    }
    
    // 可变参数方法定义
    public static void sum(int... a) {
        // 本质：可变参数在方法内部就是一个数组
        System.out.println("个数：" + a.length);
        System.out.println("内容: " + Arrays.toString(a));
        
        int sum = 0;
        for (int i : a) {
            sum += i;
        }
        System.out.println(sum);
    }
}
```

**运行结果**：
```
个数：0
内容: []
0
个数：2
内容: [10, 20]
30
个数：3
内容: [10, 20, 30]
60
```

### 可变参数的优势
- **灵活性**：可以传递任意数量的参数
- **简洁性**：避免了方法重载的复杂性
- **兼容性**：可以传递数组或单个元素

## 🛠️ Collections工具类 - 集合操作利器

### 核心概念
- Collections是Java提供的集合工具类
- 提供了大量静态方法用于操作集合
- 主要功能：排序、查找、替换、同步等
- 类似于Arrays类对数组的作用

### 1. Collections基本操作 (CollectionsTest1)

```java
public class CollectionsTest1 {
    public static void main(String[] args) {
        List<String> names = new ArrayList<>();
        Collections.addAll(names, "a11", "b22", "c33");
        System.out.println(names); // [a11, b22, c33]
        
        // 打乱集合元素顺序
        Collections.shuffle(names);
        System.out.println(names); // [b22, a11, c33] - 随机顺序
        
        // 自定义对象排序
        List<Student> students = new ArrayList<>();
        students.add(new Student("张三", 18, "男", 1.75));
        students.add(new Student("王五", 20, "男", 1.85));
        students.add(new Student("李四", 22, "男", 1.8));
        
        System.out.println(students);
        
        // 按年龄排序
        Collections.sort(students, new Comparator<Student>() {
            @Override
            public int compare(Student o1, Student o2) {
                return o2.getAge() - o1.getAge(); // 降序
            }
        });
        
        System.out.println(students);
    }
}

class Student {
    private String name;
    private int age;
    private String gender;
    private double height;
    
    // 构造器、getter、setter、toString方法
    public Student(String name, int age, String gender, double height) {
        this.name = name;
        this.age = age;
        this.gender = gender;
        this.height = height;
    }
    
    @Override
    public String toString() {
        return "Student{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", gender=" + gender +
                ", height=" + height +
                '}';
    }
}
```

**运行结果**：
```
[a11, b22, c33]
[b22, a11, c33]
[Student{name='张三', age=18, gender=男, height=1.75}
, Student{name='王五', age=20, gender=男, height=1.85}
, Student{name='李四', age=22, gender=男, height=1.8}
]
[Student{name='王五', age=20, gender=男, height=1.85}
, Student{name='李四', age=22, gender=男, height=1.8}
, Student{name='张三', age=18, gender=男, height=1.75}
]
```

### Collections常用方法
- **addAll(Collection, T...)**：批量添加元素
- **shuffle(List)**：随机打乱元素顺序
- **sort(List)**：自然排序
- **sort(List, Comparator)**：自定义排序
- **reverse(List)**：反转集合
- **max(Collection)**：获取最大元素
- **min(Collection)**：获取最小元素

## 🗺️ Map接口 - 键值对数据结构

### 核心概念
- Map是一种键值对(Key-Value)的数据结构
- 键(Key)唯一，值(Value)可重复
- 主要实现类：HashMap、LinkedHashMap、TreeMap
- 不是Collection的子接口，是独立的接口体系

### 1. Map基本操作 (MapDemo1)

```java
public class MapDemo1 {
    public static void main(String[] args) {
        Map<String, Integer> map = new HashMap<>();
        
        // 添加键值对
        map.put("华为手表", 3);
        map.put("java入门到跑路", 2);
        map.put("Iphone15", 10);
        map.put("mate60", 15);
        map.put(null, null); // HashMap允许null键和null值
        
        System.out.println(map);
        // {null=null, mate60=15, 华为手表=3, java入门到跑路=2, Iphone15=10}
    }
}
```

**运行结果**：
```
{null=null, mate60=15, 华为手表=3, java入门到跑路=2, Iphone15=10}
```

### 2. Map常用方法 (MapDemo2)

```java
public class MapDemo2 {
    public static void main(String[] args) {
        Map<String, Integer> map = new HashMap<>();
        map.put("华为手表", 3);
        map.put("java入门到跑路", 2);
        map.put("Iphone15", 10);
        map.put("mate60", 15);
        map.put(null, null);
        
        // 1. 获取集合大小
        System.out.println(map.size()); // 5
        
        // 2. 判断是否为空
        System.out.println(map.isEmpty()); // false
        
        // 3. 根据键获取值
        System.out.println(map.get("mate60")); // 15
        System.out.println(map.get("华为手表")); // 3
        
        // 4. 判断是否包含某个键
        System.out.println(map.containsKey("java入门到跑路")); // true
        
        // 5. 判断是否包含某个值
        System.out.println(map.containsValue(10)); // true
        
        // 6. 获取所有键
        Set<String> keys = map.keySet();
        System.out.println(keys); // [null, mate60, java入门到跑路, Iphone15]
        
        // 7. 获取所有值
        Collection<Integer> values = map.values();
        System.out.println(values); // [null, 15, 2, 10]
        
        // 8. 清空集合
        map.clear();
        System.out.println(map.size()); // 0
    }
}
```

**运行结果**：
```
5
false
15
3
true
true
[null, mate60, java入门到跑路, Iphone15]
[null, 15, 2, 10]
0
```

## 🔄 Map遍历 - 三种遍历方式

### 1. 键找值遍历 (MapDemo1)

```java
public class MapDemo1 {
    public static void main(String[] args) {
        Map<String, Integer> map = new HashMap<>();
        map.put("华为手表", 3);
        map.put("java入门到跑路", 2);
        map.put("Iphone15", 10);
        map.put("mate60", 15);
        map.put(null, null);
        
        System.out.println(map);
        
        // 方式一：键找值遍历
        Set<String> keys = map.keySet();
        for (String key : keys) {
            Integer value = map.get(key);
            System.out.println(key + "    " + value);
        }
    }
}
```

**运行结果**：
```
{null=null, mate60=15, 华为手表=3, java入门到跑路=2, Iphone15=10}
null    null
mate60    15
华为手表    3
java入门到跑路    2
Iphone15    10
```

### 2. 键值对遍历 (MapDemo2)

```java
public class MapDemo2 {
    public static void main(String[] args) {
        Map<String, Double> map = new HashMap<>();
        map.put("蜘蛛精", 77.7);
        map.put("牛魔王", 99.9);
        map.put("小龙女", 66.6);
        map.put("孙悟空", 88.8);
        
        System.out.println(map);
        
        // 方式二：键值对遍历
        Set<Map.Entry<String, Double>> entries = map.entrySet();
        for (Map.Entry<String, Double> entry : entries) {
            String key = entry.getKey();
            Double value = entry.getValue();
            System.out.println(key + "    " + value);
        }
    }
}
```

**运行结果**：
```
{蜘蛛精=77.7, 牛魔王=99.9, 小龙女=66.6, 孙悟空=88.8}
蜘蛛精    77.7
牛魔王    99.9
小龙女    66.6
孙悟空    88.8
```

### 3. Lambda表达式遍历 (MapDemo3)

```java
public class MapDemo3 {
    public static void main(String[] args) {
        Map<String, Double> map = new HashMap<>();
        map.put("蜘蛛精", 77.7);
        map.put("牛魔王", 99.9);
        map.put("小龙女", 66.6);
        map.put("孙悟空", 88.8);
        
        System.out.println(map);
        
        // 方式三：Lambda表达式遍历
        map.forEach((key, value) -> {
            System.out.println(key + "    " + value);
        });
        
        // 或者使用方法引用
        map.forEach((key, value) -> System.out.println(key + "    " + value));
    }
}
```

**运行结果**：
```
{蜘蛛精=77.7, 牛魔王=99.9, 小龙女=66.6, 孙悟空=88.8}
蜘蛛精    77.7
牛魔王    99.9
小龙女    66.6
孙悟空    88.8
蜘蛛精    77.7
牛魔王    99.9
小龙女    66.6
孙悟空    88.8
```

## 📊 Map实际应用 - 统计字符出现次数 (MapTest)

```java
public class MapTest {
    public static void main(String[] args) {
        // 统计字符出现次数
        List<String> data = new ArrayList<>();
        String[] selects = {"A", "B", "C", "D"};
        Random r = new Random();
        
        // 模拟80个随机选择
        for (int i = 0; i < 80; i++) {
            int index = r.nextInt(4);
            data.add(selects[index]);
        }
        
        System.out.println(data);
        
        // 使用Map统计每个选项出现的次数
        Map<String, Integer> result = new HashMap<>();
        
        for (String s : data) {
            if (result.containsKey(s)) {
                // 已存在，次数+1
                result.put(s, result.get(s) + 1);
            } else {
                // 不存在，初始化为1
                result.put(s, 1);
            }
        }
        
        // 输出统计结果
        result.forEach((key, value) -> {
            System.out.println(key + "    " + value);
        });
    }
}
```

**运行结果**：
```
[b, d, b, c, d, d, b, b, a, a, a, c, a, c, b, c, d, b, d, b, d, c, a, d, b, a, a, c, a, a, c, c, d, a, b, a, b, d, a, c, d, d, c, b, d, b, c, a, c, d, c, a, a, b, c, c, c, b, a, d, b, a, d, b, d, c, b, c, d, a, d, b, a, d, a, c, c, a, a, b]
a    22
b    19
c    20
d    19
```

## 🃏 综合案例 - 斗地主发牌系统 (Ex1)

### 案例需求
设计一个斗地主发牌系统，实现以下功能：
1. 准备54张牌（52张普通牌 + 2张王牌）
2. 洗牌（随机打乱牌的顺序）
3. 发牌（3个玩家每人17张牌，剩余3张作为底牌）
4. 看牌（按牌的大小排序显示）

### 1. 扑克牌类设计 (Card)

```java
public class Card {
    private String number; // 牌面数字
    private String color;  // 花色
    private int size;      // 牌的大小（用于排序）

    public Card() {
    }

    public Card(String number, String color, int size) {
        this.number = number;
        this.color = color;
        this.size = size;
    }

    // getter和setter方法
    public String getNumber() { return number; }
    public void setNumber(String number) { this.number = number; }
    public String getColor() { return color; }
    public void setColor(String color) { this.color = color; }
    public int getSize() { return size; }
    public void setSize(int size) { this.size = size; }

    @Override
    public String toString() {
        return number + color;
    }
}
```

### 2. 房间类设计 (Room)

```java
public class Room {
    private List<Card> allCard = new ArrayList<>();

    public Room() {
        // 1. 做牌：准备54张牌
        String[] numbers = {"3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K", "A", "2"};
        String[] colors = {"♠", "♥", "♣", "♦"};

        int size = 0; // 牌的大小

        // 制作52张普通牌
        for (String number : numbers) {
            size++;
            for (String color : colors) {
                allCard.add(new Card(number, color, size));
            }
        }

        // 制作2张王牌
        allCard.add(new Card("小王", "", ++size));
        allCard.add(new Card("大王", "", ++size));
    }

    public void start() {
        // 2. 洗牌
        Collections.shuffle(allCard);
        System.out.println("洗牌：" + allCard);

        // 3. 发牌：3个玩家，每人17张牌，剩余3张作为底牌
        List<Card> player1 = new ArrayList<>();
        List<Card> player2 = new ArrayList<>();
        List<Card> player3 = new ArrayList<>();
        List<Card> lastCard = new ArrayList<>();

        // 轮流发牌
        for (int i = 0; i < allCard.size(); i++) {
            Card c = allCard.get(i);
            if (i >= allCard.size() - 3) {
                // 最后3张作为底牌
                lastCard.add(c);
            } else if (i % 3 == 0) {
                player1.add(c);
            } else if (i % 3 == 1) {
                player2.add(c);
            } else {
                player3.add(c);
            }
        }

        // 4. 排序（按牌的大小排序）
        sortCard(player1);
        sortCard(player2);
        sortCard(player3);

        // 5. 看牌
        System.out.println("玩家1：" + player1);
        System.out.println("玩家2：" + player2);
        System.out.println("玩家3：" + player3);
        System.out.println("底牌：" + lastCard);
    }

    private void sortCard(List<Card> cards) {
        Collections.sort(cards, new Comparator<Card>() {
            @Override
            public int compare(Card o1, Card o2) {
                return o2.getSize() - o1.getSize(); // 降序排列
            }
        });
    }
}
```

### 3. 测试类 (Test)

```java
public class Test {
    public static void main(String[] args) {
        Room room = new Room();
        room.start();
    }
}
```

**运行结果**：
```
新牌：[3♠, 3♥, 3♣, 3♦, 4♠, 4♥, 4♣, 4♦, 5♠, 5♥, 5♣, 5♦, 6♠, 6♥, 6♣, 6♦, 7♠, 7♥, 7♣, 7♦, 8♠, 8♥, 8♣, 8♦, 9♠, 9♥, 9♣, 9♦, 10♠, 10♥, 10♣, 10♦, J♠, J♥, J♣, J♦, Q♠, Q♥, Q♣, Q♦, K♠, K♥, K♣, K♦, A♠, A♥, A♣, A♦, 2♠, 2♥, 2♣, 2♦, 小王, 大王]
洗牌：[4♦, J♠, 9♠, A♥, 4♣, J♥, 7♠, 9♥, 4♠, A♣, 大王, Q♦, 3♠, 2♠, Q♥, 7♥, 3♥, A♠, 8♠, 3♣, 10♠, 7♣, 5♠, 8♥, 8♣, Q♠, 9♣, 2♥, 7♦, K♠, 6♠, 6♥, 10♥, 6♣, 4♥, 6♦, 9♦, K♥, 5♥, 小王, 2♣, K♣, 5♣, 8♦, 2♦, J♣, K♦, J♦, A♦, 10♣, Q♣, 3♦, 10♦, 5♦]
玩家1：[小王, 2♣, A♦, A♣, A♠, J♣, 9♣, 8♠, 8♣, 7♠, 7♥, 7♣, 6♠, 6♥, 5♠, 4♦, 3♠]
玩家2：[大王, 2♠, 2♥, K♠, K♥, Q♦, Q♥, J♠, J♥, 9♠, 8♥, 6♣, 4♣, 4♠, 3♥, 3♣, 3♦]
玩家3：[2♦, A♥, K♣, K♦, Q♠, J♦, 10♠, 10♥, 9♥, 9♦, 8♦, 7♦, 6♦, 5♥, 5♣, 4♥, 10♣]
底牌：[Q♣, 10♦, 5♦]
```

## 🎯 学习总结

### 核心知识点
1. **可变参数**：提供方法参数的灵活性，本质是数组
2. **Collections工具类**：提供丰富的集合操作方法
3. **Map接口**：键值对数据结构，键唯一值可重复
4. **Map遍历**：键找值、键值对、Lambda三种方式
5. **实际应用**：统计分析、游戏开发等场景

### Map实现类特点
- **HashMap**：基于哈希表，无序，允许null键值，线程不安全
- **LinkedHashMap**：基于哈希表+链表，保持插入顺序
- **TreeMap**：基于红黑树，自动排序，不允许null键

### 最佳实践
- **可变参数**：适用于参数数量不确定的场景
- **Collections**：优先使用工具类方法而非手动实现
- **Map选择**：根据是否需要排序和顺序选择合适的实现类
- **遍历方式**：Lambda表达式简洁高效，推荐使用

Day07将集合框架的应用提升到新高度，Map接口的引入为数据处理提供了强大工具，结合实际案例加深了对集合框架的理解和应用能力。

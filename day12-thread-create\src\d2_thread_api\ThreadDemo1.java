package d2_thread_api;

/**
 * ========== Thread类常用方法和构造器总结 ==========
 *
 * 【Thread提供的常用方法】
 *
 * 1. 线程执行相关：
 *    public void run()                    // 线程执行的任务代码
 *    public void start()                  // 启动线程
 *
 * 2. 线程信息获取/设置：
 *    public String getName()              // 获取线程名称
 *    public void setName(String name)     // 为线程设置名称
 *
 * 3. 线程控制相关：
 *    public static Thread currentThread() // 获取当前正在执行的线程对象
 *    public static void sleep(long time)  // 让当前线程休眠指定毫秒
 *    public final void join()...          // 让调用线程等待当前线程执行完毕
 *
 * 【Thread提供的常见构造器】
 *
 *    public Thread(String name)                        // 可以为线程指定名称
 *    public Thread(Runnable target)                    // 封装Runnable对象
 *    public Thread(Runnable target, String name)       // 封装Runnable对象并指定名称
 *
 * 【使用建议】
 * 1. 总是使用start()启动线程，不要直接调用run()
 * 2. 为线程设置有意义的名称，便于调试
 * 3. 合理使用sleep()控制线程执行节奏
 * 4. 使用join()实现线程间的协调等待
 *
 * 【方法详细说明】
 * - run(): 定义线程要执行的任务，需要重写
 * - start(): 启动线程，JVM会调用run()方法
 * - sleep(time): 让线程暂停执行指定时间（毫秒）
 * - join(): 等待该线程执行完毕后再继续
 * - getName()/setName(): 线程命名，便于调试和管理
 * - currentThread(): 获取当前线程引用，常用于调试
 */



public class ThreadDemo1 {
    public static void main(String[] args) {
        // 3. 创建线程对象 代表具体的线程
        Thread t = new MyThread("线程1");
        //t.setName("MyThread0");   //设计名字 在启动前设计
        // 4.启动线程  会自动执行run方法
        t.start();
        System.out.println(t.getName());
        //不能直接调用run  cpu不会注册新线程执行 此时相当于单线程

        Thread t1 = new MyThread("线程2");
        t1.start();
        System.out.println(t1.getName());

        //这个代码是哪个线程在执行，就会得到哪个线程对象
        Thread m = Thread.currentThread();
        System.out.println(m.getName());


        // 主线程执行的任务    主线程要放在启动子线程之后      否则相当于单线程
        for (int i = 0; i < 5; i++) {
            System.out.println("主线程输出：" + i);
        }
    }
}

//1.定义一个为继承thread类，成为线程类
class MyThread extends Thread {

    public MyThread(String name) {
        super(name);
    }

    //2.重写run方法，声明线程要干的事
    @Override
    public void run() {
        for (int i = 0; i < 5; i++) {
            System.out.println(Thread.currentThread().getName()+"子线程输出：" + i);
        }
    }
}
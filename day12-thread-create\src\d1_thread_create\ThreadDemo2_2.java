package d1_thread_create;

public class ThreadDemo2_2 {
    public static void main(String[] args) {
        Runnable r1 = new Runnable() {
            @Override
            public void run() {
                for (int i = 0; i < 5; i++) {
                    System.out.println("子线程1 " + i);
                }
            }
        };

        Thread t1 = new Thread(r1);

        t1.start();

        //构造器中使用匿名内部类
        new Thread( ()-> {
            for (int i = 0; i < 5; i++) {
                System.out.println("子线程2 " + i);
            }
        }).start();


        for (int i = 0; i < 5; i++) {
            System.out.println("主线程 " + i);
        }







    }
}

import java.util.*;
import java.util.stream.Collectors;

/**
 * Stream流练习题 - 综合应用
 * 请完成以下练习题，使用Stream流来解决问题
 */
public class StreamExercise3 {
    public static void main(String[] args) {
        // 员工数据
        List<Employee> employees = Arrays.asList(
            new Employee("张三", "技术部", 8000),
            new Employee("李四", "销售部", 6000),
            new Employee("王五", "技术部", 9000),
            new Employee("张六", "人事部", 5500),
            new Employee("赵七", "技术部", 12000),
            new Employee("钱八", "销售部", 7000),
            new Employee("孙九", "财务部", 6500),
            new Employee("周十", "技术部", 10000),
            new Employee("张伟", "销售部", 8500),
            new Employee("李娜", "人事部", 6000)
        );
        
        // 产品数据
        List<Product> products = Arrays.asList(
            new Product("iPhone14", "电子产品", 5999.0),
            new Product("MacBook", "电子产品", 12999.0),
            new Product("Nike鞋", "服装", 899.0),
            new Product("Adidas衣服", "服装", 299.0),
            new Product("小米手机", "电子产品", 2999.0),
            new Product("华为平板", "电子产品", 3999.0),
            new Product("李宁运动鞋", "服装", 599.0),
            new Product("戴尔笔记本", "电子产品", 4999.0)
        );
        
        System.out.println("=== Stream流综合练习题 ===");
        
        // 题目1：找出技术部薪资最高的员工
        System.out.println("题目1：找出技术部薪资最高的员工");
        // TODO: 使用Stream流找出技术部薪资最高的员工，并打印结果
        
        
        // 题目2：按部门分组，计算各部门平均薪资
        System.out.println("\n题目2：按部门分组，计算各部门平均薪资");
        // TODO: 使用Stream流按部门分组，计算各部门平均薪资，并打印结果
        
        
        // 题目3：找出薪资前3名的员工姓名
        System.out.println("\n题目3：找出薪资前3名的员工姓名");
        // TODO: 使用Stream流找出薪资前3名的员工姓名，并打印结果
        
        
        // 题目4：统计各部门人数
        System.out.println("\n题目4：统计各部门人数");
        // TODO: 使用Stream流统计各部门人数，并打印结果
        
        
        // 题目5：找出价格在1000-10000之间的电子产品
        System.out.println("\n题目5：找出价格在1000-10000之间的电子产品");
        // TODO: 使用Stream流找出价格在1000-10000之间的电子产品，并打印结果
        
        
        // 题目6：按产品类别分组，计算各类别商品数量
        System.out.println("\n题目6：按产品类别分组，计算各类别商品数量");
        // TODO: 使用Stream流按产品类别分组，计算各类别商品数量，并打印结果
        
        
        // 题目7：找出最贵的服装产品
        System.out.println("\n题目7：找出最贵的服装产品");
        // TODO: 使用Stream流找出最贵的服装产品，并打印结果
        
        
        // 题目8：计算所有电子产品的总价值
        System.out.println("\n题目8：计算所有电子产品的总价值");
        // TODO: 使用Stream流计算所有电子产品的总价值，并打印结果
        
        
        // 题目9：判断是否有员工薪资超过10000
        System.out.println("\n题目9：判断是否有员工薪资超过10000");
        // TODO: 使用Stream流判断是否有员工薪资超过10000，并打印结果
        
        
        // 题目10：创建一个包含所有部门名称的去重列表
        System.out.println("\n题目10：创建一个包含所有部门名称的去重列表");
        // TODO: 使用Stream流创建包含所有部门名称的去重列表，并打印结果
        
    }
}

// 员工类
class Employee {
    private String name;
    private String department;
    private double salary;
    
    public Employee(String name, String department, double salary) {
        this.name = name;
        this.department = department;
        this.salary = salary;
    }
    
    public String getName() { return name; }
    public String getDepartment() { return department; }
    public double getSalary() { return salary; }
    
    @Override
    public String toString() {
        return String.format("Employee{name='%s', dept='%s', salary=%.0f}", name, department, salary);
    }
}

// 产品类
class Product {
    private String name;
    private String category;
    private double price;
    
    public Product(String name, String category, double price) {
        this.name = name;
        this.category = category;
        this.price = price;
    }
    
    public String getName() { return name; }
    public String getCategory() { return category; }
    public double getPrice() { return price; }
    
    @Override
    public String toString() {
        return String.format("Product{name='%s', category='%s', price=%.1f}", name, category, price);
    }
}

# 练习6：题目修订说明 🃏

## 🔄 题目修订内容

### 原题目要求：
- 52张标准扑克牌
- 点数：A、2-10、J、Q、K
- A=1，J=11，Q=12，K=13

### 修订后要求：
- **54张扑克牌**（包含大小王）
- **点数排序**：3-K(3-13), A(14), 2(15), 小王(16), 大王(17)
- **花色**：红桃、黑桃、方块、梅花、王

## 📋 具体修订内容

### 1. 枚举定义修订

**Suit枚举：**
```java
// 原要求：4种花色
HEARTS, SPADES, DIAMONDS, CLUBS

// 修订为：5种花色（增加王牌花色）
HEARTS, SPADES, DIAMONDS, CLUBS, JOKER
```

**Rank枚举：**
```java
// 原要求：13种点数
ACE(1), TWO(2), ..., KING(13)

// 修订为：15种点数（增加大小王）
THREE(3), ..., KING(13), ACE(14), TWO(15), SMALL_JOKER(16), BIG_JOKER(17)
```

### 2. Card类修订

**属性要求：**
```java
// 强调：必须使用枚举类型
private final Suit suit;   // 不是String
private final Rank rank;   // 不是String
```

**新增方法：**
- `isJoker()`：判断是否为王牌
- `isRed()`：判断是否为红色牌（大王为红色）
- `isBlack()`：判断是否为黑色牌（小王为黑色）

### 3. Deck类修订

**牌组数量：**
- 原要求：52张牌
- 修订为：54张牌（52张标准牌 + 2张王牌）

**新增方法：**
- `sortByValue()`：按点数排序

### 4. 测试要求修订

**新增测试内容：**
- 测试54张牌的创建
- 测试王牌的特殊功能
- 测试点数排序（3-K, A, 2, 小王, 大王）

## 🎯 修订原因

### 1. **用户需求**
用户明确要求包含大小王，这是中国扑克牌游戏的常见需求。

### 2. **点数排序逻辑**
按照中国扑克牌游戏习惯：
- 3是最小的牌
- A和2是大牌（在K之后）
- 王牌是最大的牌

### 3. **枚举应用强化**
通过包含王牌，更好地展示枚举的灵活性：
- 特殊值处理（王牌）
- 条件判断方法（isJoker）
- 枚举在类中的应用

## 📊 修订对比表

| 项目 | 原要求 | 修订后 | 修订原因 |
|------|--------|--------|----------|
| 牌组数量 | 52张 | 54张 | 包含大小王 |
| 花色数量 | 4种 | 5种 | 增加王牌花色 |
| 点数数量 | 13种 | 15种 | 增加大小王 |
| A的点数 | 1 | 14 | 按中国习惯，A为大牌 |
| 2的点数 | 2 | 15 | 按中国习惯，2为大牌 |
| 最大牌 | K(13) | 大王(17) | 王牌最大 |
| Card属性 | 未明确 | 必须用枚举 | 强化枚举应用 |

## 🔧 对现有代码的影响

### 1. **Rank枚举需要调整**
```java
// 需要修正的点数值
ACE("A", 14),     // 从1改为14
TWO("2", 15),     // 从2改为15

// 需要规范化的王牌命名
SMALL_JOKER("小王", 16),  // 从Joker改名
BIG_JOKER("大王", 17);    // 从Joker2改名
```

### 2. **Card类需要重构**
```java
// 必须改为使用枚举
private final Suit suit;   // 替换String类型
private final Rank rank;   // 替换String类型
```

### 3. **Deck类需要实现**
- 创建54张牌的逻辑
- 王牌的特殊处理
- 排序算法的实现

## 🎯 修订后的学习目标

### 1. **枚举高级应用**
- 枚举作为类属性
- 枚举的条件判断方法
- 枚举的特殊值处理

### 2. **面向对象设计**
- 类之间的协作
- 封装和数据隐藏
- 方法的职责分离

### 3. **集合和算法**
- 列表操作
- 排序算法
- 比较器的使用

## 📋 更新后的题目文件

**已更新文件：** `Ex/基础练习题集.md`

**更新内容：**
- 题目标题增加"（含大小王）"
- 明确54张牌的要求
- 详细说明点数排序规则
- 强调Card类必须使用枚举属性
- 增加王牌相关的测试要求

## 🚀 下一步建议

1. **立即修复代码**：按照修订后的要求调整现有代码
2. **理解设计意图**：枚举在类中的正确应用方式
3. **完善功能实现**：实现Deck类的完整功能
4. **测试验证**：确保54张牌的正确性

**这次修订让练习6更贴近实际应用，同时强化了枚举的核心概念！** 🃏✨

package exercise1;

/**
 * 练习1：银行账户类
 * 
 * 要求实现：
 * 1. 账户号码（accountNumber，String类型）
 * 2. 账户余额（balance，double类型）  
 * 3. 账户持有人姓名（holderName，String类型）
 * 4. 构造方法：初始化账户信息
 * 5. deposit(double amount)：存款方法
 * 6. withdraw(double amount)：取款方法（余额不足时输出提示）
 * 7. getBalance()：查询余额
 * 8. toString()：返回账户信息字符串
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class BankAccount {
    
    // TODO: 在这里定义类的属性
    private String accountNumber;
    private double balance;
    private String holderName;
    
    // TODO: 在这里实现构造方法
    public BankAccount() {

    }
    public BankAccount(String accountNumber, double balance, String holderName) {
        this.accountNumber = accountNumber;
        this.balance = balance;
        this.holderName = holderName;
    }
    
    
    // TODO: 在这里实现存款方法
    public void deposit(double amount) {
        // 你的代码
        balance += amount;
    }
    
    // TODO: 在这里实现取款方法
    public boolean withdraw(double amount) {
        if(balance - amount >= 0) {
            balance -= amount;
            return true;
        }
        // 你的代码
        return false; // 临时返回值，请修改
    }
    
    // TODO: 在这里实现查询余额方法
    public double getBalance() {
        // 你的代码
        return balance; // 临时返回值，请修改
    }
    
    // TODO: 在这里实现toString方法

    @Override
    public String toString() {
        return "BankAccount{" +
                "accountNumber='" + accountNumber + '\'' +
                ", balance=" + balance +
                ", holderName='" + holderName + '\'' +
                '}';
    }


    // TODO: 如果需要，可以添加其他getter和setter方法


    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public void setBalance(double balance) {
        this.balance = balance;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }
}

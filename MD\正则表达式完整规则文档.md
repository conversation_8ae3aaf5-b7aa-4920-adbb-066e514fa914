# 正则表达式完整规则文档

## 1. 基本匹配符

| 符号 | 说明 | 示例 |
|------|------|------|
| `.` | 匹配除换行符外的任意字符 | `a.c` 匹配 `abc`、`a1c`、`a c` |
| `^` | 匹配字符串开始位置 | `^abc` 匹配以 `abc` 开头的字符串 |
| `$` | 匹配字符串结束位置 | `abc$` 匹配以 `abc` 结尾的字符串 |
| `\` | 转义字符 | `\.` 匹配点号本身 |
| `|` | 或操作符（选择） | `cat|dog` 匹配 `cat` 或 `dog` |

## 2. 字符类

| 符号 | 说明 | 示例 |
|------|------|------|
| `[abc]` | 匹配字符集中的任意一个字符 | `[abc]` 匹配 `a`、`b` 或 `c` |
| `[^abc]` | 匹配不在字符集中的任意字符 | `[^abc]` 匹配除 `a`、`b`、`c` 外的字符 |
| `[a-z]` | 匹配范围内的任意字符 | `[a-z]` 匹配小写字母 |
| `[A-Z]` | 匹配大写字母 | `[A-Z]` 匹配大写字母 |
| `[0-9]` | 匹配数字 | `[0-9]` 匹配数字 0-9 |
| `[a-zA-Z]` | 匹配所有字母 | `[a-zA-Z]` 匹配大小写字母 |
| `[a-zA-Z0-9]` | 匹配字母和数字 | `[a-zA-Z0-9]` 匹配字母数字 |

## 3. 预定义字符类

| 符号 | 说明 | 等价于 |
|------|------|--------|
| `\d` | 匹配数字 | `[0-9]` |
| `\D` | 匹配非数字 | `[^0-9]` |
| `\w` | 匹配单词字符 | `[a-zA-Z0-9_]` |
| `\W` | 匹配非单词字符 | `[^a-zA-Z0-9_]` |
| `\s` | 匹配空白字符 | 空格、制表符、换行符等 |
| `\S` | 匹配非空白字符 | 除空白字符外的所有字符 |
| `\t` | 匹配制表符 | Tab 字符 |
| `\n` | 匹配换行符 | 换行字符 |
| `\r` | 匹配回车符 | 回车字符 |

## 4. 量词（Quantifiers）

| 符号 | 说明 | 示例 |
|------|------|------|
| `*` | 匹配前面的字符0次或多次（贪婪） | `ab*c` 匹配 `ac`、`abc`、`abbc` |
| `+` | 匹配前面的字符1次或多次（贪婪） | `ab+c` 匹配 `abc`、`abbc`，不匹配 `ac` |
| `?` | 匹配前面的字符0次或1次（贪婪） | `ab?c` 匹配 `ac`、`abc` |
| `{n}` | 匹配前面的字符恰好n次 | `ab{2}c` 匹配 `abbc` |
| `{n,}` | 匹配前面的字符至少n次 | `ab{2,}c` 匹配 `abbc`、`abbbc` |
| `{n,m}` | 匹配前面的字符n到m次 | `ab{1,3}c` 匹配 `abc`、`abbc`、`abbbc` |
| `*?` | 匹配0次或多次（非贪婪） | 尽可能少地匹配 |
| `+?` | 匹配1次或多次（非贪婪） | 尽可能少地匹配 |
| `??` | 匹配0次或1次（非贪婪） | 尽可能少地匹配 |
| `{n,m}?` | 匹配n到m次（非贪婪） | 尽可能少地匹配 |

## 5. 边界匹配

| 符号 | 说明 | 示例 |
|------|------|------|
| `^` | 匹配行的开始 | `^abc` 匹配行首的 `abc` |
| `$` | 匹配行的结束 | `abc$` 匹配行尾的 `abc` |
| `\b` | 匹配单词边界 | `\bcat\b` 匹配独立的单词 `cat` |
| `\B` | 匹配非单词边界 | `\Bcat\B` 匹配非独立的 `cat` |
| `\A` | 匹配字符串开始 | 整个字符串的开始 |
| `\Z` | 匹配字符串结束 | 整个字符串的结束 |

## 6. 分组和捕获

| 符号 | 说明 | 示例 |
|------|------|------|
| `()` | 捕获分组 | `(abc)+` 匹配一个或多个 `abc` |
| `(?:...)` | 非捕获分组 | `(?:abc)+` 匹配但不捕获 |
| `(?<name>...)` | 命名捕获分组 | `(?<year>\d{4})` 命名为 year |
| `\1`, `\2` | 反向引用 | `(abc)\1` 匹配 `abcabc` |

## 7. 前瞻和后瞻断言

| 符号 | 说明 | 示例 |
|------|------|------|
| `(?=...)` | 正向前瞻断言 | `\d+(?=元)` 匹配后面跟着"元"的数字 |
| `(?!...)` | 负向前瞻断言 | `\d+(?!元)` 匹配后面不跟"元"的数字 |
| `(?<=...)` | 正向后瞻断言 | `(?<=¥)\d+` 匹配前面有"¥"的数字 |
| `(?<!...)` | 负向后瞻断言 | `(?<!¥)\d+` 匹配前面没有"¥"的数字 |

## 8. 特殊字符转义

| 符号 | 说明 |
|------|------|
| `\.` | 匹配点号 |
| `\*` | 匹配星号 |
| `\+` | 匹配加号 |
| `\?` | 匹配问号 |
| `\[` | 匹配左方括号 |
| `\]` | 匹配右方括号 |
| `\{` | 匹配左花括号 |
| `\}` | 匹配右花括号 |
| `\(` | 匹配左圆括号 |
| `\)` | 匹配右圆括号 |
| `\^` | 匹配插入符号 |
| `\$` | 匹配美元符号 |
| `\|` | 匹配竖线 |
| `\\` | 匹配反斜杠 |

## 9. 修饰符（Java中的Pattern标志）

| 标志 | 说明 |
|------|------|
| `Pattern.CASE_INSENSITIVE` | 忽略大小写 |
| `Pattern.MULTILINE` | 多行模式，^和$匹配每行的开始和结束 |
| `Pattern.DOTALL` | 点号匹配包括换行符在内的所有字符 |
| `Pattern.UNICODE_CASE` | Unicode大小写匹配 |
| `Pattern.CANON_EQ` | 规范等价 |
| `Pattern.UNIX_LINES` | Unix行模式 |
| `Pattern.LITERAL` | 字面量模式 |
| `Pattern.COMMENTS` | 注释模式，忽略空白和#注释 |

## 10. 常用正则表达式模式

### 基本验证
- **邮箱**: `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
- **手机号（中国）**: `^1[3-9]\d{9}$`
- **身份证号**: `^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$`
- **IP地址**: `^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$`
- **URL**: `^(https?|ftp)://[^\s/$.?#].[^\s]*$`

### 格式验证
- **日期（YYYY-MM-DD）**: `^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$`
- **时间（HH:MM:SS）**: `^([01]?\d|2[0-3]):[0-5]\d:[0-5]\d$`
- **十六进制颜色**: `^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$`
- **MAC地址**: `^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$`
- **银行卡号**: `^\d{16,19}$`
- **邮政编码（中国）**: `^\d{6}$`

### 密码强度
- **强密码**: `^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$`
  - 至少8位
  - 包含大小写字母
  - 包含数字
  - 包含特殊字符

### 文本处理
- **中文字符**: `[\u4e00-\u9fa5]`
- **HTML标签**: `<([a-zA-Z][a-zA-Z0-9]*)\b[^>]*>(.*?)</\1>`
- **双引号内容**: `"([^"]*)"`
- **数字（包括小数）**: `^-?\d+(\.\d+)?$`

## 11. 性能优化建议

1. **使用非捕获分组** `(?:...)` 而不是捕获分组 `(...)`，除非需要提取内容
2. **使用字符类** `[abc]` 而不是选择 `(a|b|c)`
3. **将最可能匹配的选项放在前面**
4. **避免嵌套量词**，如 `(a+)+`
5. **使用具体的量词**而不是贪婪量词
6. **在字符串开始处使用锚点** `^` 可以提高性能
7. **预编译正则表达式模式**，避免重复编译

## 12. 调试技巧

1. 使用在线正则表达式测试工具
2. 逐步构建复杂的正则表达式
3. 使用注释模式来添加说明
4. 测试边界情况和异常输入
5. 考虑使用多个简单的正则表达式而不是一个复杂的

## 13. 贪婪 vs 非贪婪匹配

### 贪婪匹配（默认）
- 尽可能多地匹配字符
- `.*` 会匹配尽可能长的字符串

### 非贪婪匹配
- 尽可能少地匹配字符
- `.*?` 会匹配尽可能短的字符串

### 示例
对于字符串 `<div>Hello</div><span>World</span>`：
- 贪婪匹配 `<.*>` 会匹配整个字符串
- 非贪婪匹配 `<.*?>` 会分别匹配 `<div>`、`</div>`、`<span>`、`</span>`

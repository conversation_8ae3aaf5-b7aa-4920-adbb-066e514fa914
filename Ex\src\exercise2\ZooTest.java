package exercise2;

/**
 * 练习2：动物园管理测试
 * 
 * 测试要求：
 * 1. 创建不同类型的动物对象
 * 2. 添加到动物园中
 * 3. 调用makeAllSounds()展示多态效果
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class ZooTest {
    
    public static void main(String[] args) {
        System.out.println("=== 动物园管理系统测试 ===\n");
        
        // TODO: 创建动物园
        Zoo zoo = new Zoo();
        
        // TODO: 创建不同类型的动物
        // 提示：Animal dog = new Dog("旺财", 3);
        Animal dog  = new Dog("旺财", 3);

        Animal Cat = new Cat("花花",2);

        Bird bird = new Bird("小鸟",2);



        
        // TODO: 将动物添加到动物园
        zoo.animals.add(dog);
        zoo.animals.add(Cat);
        zoo.animals.add(bird);
        
        // TODO: 显示所有动物信息
        System.out.println("--- 动物园动物信息 ---");
        zoo.showAllAnimals();

        
        // TODO: 让所有动物发声（多态演示）
        System.out.println("\n--- 动物们的声音（多态演示） ---");
        zoo.makeAllSounds();
        
        
        // TODO: 显示动物园统计信息
        System.out.println("\n--- 动物园统计 ---");
        zoo.getAnimalCount();
        
        
        System.out.println("\n=== 测试完成 ===");
    }
}

package exercise6;

/**
 * 练习6：扑克牌花色枚举
 *
 * 要求：
 * 1. 定义四种花色：红桃、黑桃、方块、梅花
 * 2. 每种花色有中文名称和符号
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public enum Suit {

    // TODO: 定义四种花色
    // 提示：HEARTS("红桃", "♥"), SPADES("黑桃", "♠"), DIAMONDS("方块", "♦"), CLUBS("梅花", "♣");
    HEARTS("红桃", "♥"), SPADES("黑桃", "♠"), DIAMONDS("方块", "♦"), CLUBS("梅花", "♣");

    // TODO: 定义属性
    private String symbol;
    private String suit;

    // TODO: 构造方法
    Suit(String symbol, String suit) {
        this.symbol = symbol;
        this.suit = suit;
    }


    // TODO: getter方法

    public String getSymbol() {
        return symbol;
    }

    public String getSuit() {
        return suit;
    }


    // TODO: toString方法


    @Override
    public String toString() {
        return "Suit{" +
                "symbol='" + symbol + '\'' +
                ", suit='" + suit + '\'' +
                '}';
    }
}

package exercise3;

import java.util.Objects;

/**
 * 练习3：学生类
 * 
 * 要求实现：
 * 1. 学号（id）、姓名（name）、成绩（score）属性
 * 2. 实现equals()和hashCode()方法
 * 3. 实现Comparable<Student>接口（按成绩排序）
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class Student implements Comparable<Student> {
    
    // TODO: 定义属性
    private  String id;
    private String name;
    private double score;

    
    // TODO: 构造方法
    public Student() {}

    public Student(String id, String name, double score) {
        this.id = id;
        this.name = name;
        this.score = score;
    }
    
    
    // TODO: getter和setter方法


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public double getScore() {
        return score;
    }

    public void setScore(double score) {
        this.score = score;
    }

    // TODO: 实现equals方法（基于学号比较）
    // TODO: 实现hashCode方法


    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        Student student = (Student) o;
        return id == student.id && Double.compare(score, student.score) == 0 && Objects.equals(name, student.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, score);
    }

    // TODO: 实现compareTo方法（按成绩降序排序）
    @Override
    public int compareTo(Student other) {
        return Double.compare(this.score, other.score);
    }
    
    // TODO: 实现toString方法

    @Override
    public String toString() {
        return "Student{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", score=" + score +
                '}';
    }
}

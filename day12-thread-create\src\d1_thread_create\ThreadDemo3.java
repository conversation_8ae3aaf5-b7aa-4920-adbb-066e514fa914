package d1_thread_create;

import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;

public class ThreadDemo3 {

    public static void main(String[] args) {
        //3.创建Callable 对象
        Callable<String> call = new MyCallable(100);

        //4.将 Callable 封装成  FutureTask 对象
        /// 未来任务对象有两个作用    它是一个 runnable对象  它可以获取线程执行后的结果
        FutureTask<String> task = new FutureTask<String>(call);

        //5.将未来任务对象交给线程对象
        Thread t = new Thread(task);

        //6.启动线程
        t.start();



        Callable<String> call2 = new MyCallable(100);
        FutureTask<String> task2 = new FutureTask<String>(call2);
        Thread t2 = new Thread(task2);
        t2.start();

        try{
            //如果第一个线程没有执行完毕，会在这里等待第一个线程执行完毕后，再取结果
            String s = task.get();
            System.out.println(s);
        }catch(Exception e){
            e.printStackTrace();
        }
    }

}

// 1.定义一个任务类   实现Callabe接口
class MyCallable implements Callable<String> {
    //2. 重写call方法   声明任务 并返回 结果
    private int n;

    public MyCallable(int n) {
        this.n = n;
    }

    @Override
    public String call() throws Exception {
        int sum = 0;
        for(int i=1;i<=n;i++){
            sum+=i;
        }
        return "子线程求和"+sum;
    }
}
package d3_thread_safe;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Account {
    private String cardId;
    private double money;

    public void drawMoney(double money){
        String name = Thread.currentThread().getName();
        // 线程安全问题：检查余额和扣款之间可能被其他线程打断
        if(this.money >= money){
            //更容易出现安全问题
            System.out.println(name + "来取钱了，" + name + ": " + this.money);
            this.money -= money;
            System.out.println(name + "取钱成功，账户余额: " + this.money);

        } else {
            System.out.println(name + "余额不足: " + this.money);
        }
    }
}

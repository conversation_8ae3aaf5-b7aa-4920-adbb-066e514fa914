# Switch语句中Default分支的正确处理 🎯

## 🚨 你的Default处理问题

### 你的错误代码：
```java
default:
    try{
        return;  // ❌ 错误：return后面没有值
    }catch(ArithmeticException e){
        throw new InvalidOperatorException("操作不在此");
    }
```

### 问题分析：
1. **`return;` 错误**：方法返回类型是`double`，不能用空的`return`
2. **逻辑错误**：没有任何可能抛出`ArithmeticException`的代码
3. **try-catch多余**：default分支应该直接抛出异常

## ✅ 正确的Default处理方式

### 方式1：直接抛出异常（推荐）
```java
public double calculate(double a, double b, String operator) 
        throws DivideByZeroException, InvalidOperatorException {
    
    switch (operator) {
        case "+":
            return add(a, b);
        case "-":
            return subtract(a, b);
        case "*":
            return multiply(a, b);
        case "/":
            return divide(a, b);
        default:
            throw new InvalidOperatorException("不支持的操作符：" + operator);
    }
}
```

### 方式2：先检查null再处理
```java
public double calculate(double a, double b, String operator) 
        throws DivideByZeroException, InvalidOperatorException {
    
    // 先检查null
    if (operator == null) {
        throw new InvalidOperatorException("操作符不能为空");
    }
    
    switch (operator) {
        case "+":
            return add(a, b);
        case "-":
            return subtract(a, b);
        case "*":
            return multiply(a, b);
        case "/":
            return divide(a, b);
        default:
            throw new InvalidOperatorException("不支持的操作符：" + operator);
    }
}
```

## 🔍 Switch语句的Default分支规则

### 1. Default分支的作用
- 处理所有未明确列出的case
- 提供默认行为或错误处理
- 确保所有可能的输入都有对应的处理

### 2. Default分支的位置
```java
switch (value) {
    case "A":
        return 1;
    case "B":
        return 2;
    default:  // 可以放在任何位置，但通常放在最后
        throw new IllegalArgumentException("无效值：" + value);
}
```

### 3. Default分支的返回值
```java
// ✅ 正确：返回合适的值
switch (operator) {
    case "+": return add(a, b);
    case "-": return subtract(a, b);
    default: throw new InvalidOperatorException("无效操作符");
}

// ❌ 错误：返回类型不匹配
switch (operator) {
    case "+": return add(a, b);
    default: return;  // 错误：方法返回double，不能空return
}
```

## 🎯 异常处理的最佳实践

### 1. 直接抛出异常（推荐）
```java
default:
    throw new InvalidOperatorException("不支持的操作符：" + operator);
```

**优点**：
- 简洁明了
- 直接表达错误意图
- 不需要额外的try-catch

### 2. 记录日志后抛出异常
```java
default:
    System.err.println("收到无效操作符：" + operator);
    throw new InvalidOperatorException("不支持的操作符：" + operator);
```

### 3. 提供详细的错误信息
```java
default:
    String supportedOps = "支持的操作符：+, -, *, /";
    throw new InvalidOperatorException(
        "不支持的操作符：'" + operator + "'. " + supportedOps
    );
```

## 🚫 常见的Default处理错误

### 错误1：空的return
```java
// ❌ 错误
default:
    return;  // 方法返回double，不能空return
```

### 错误2：返回错误的默认值
```java
// ❌ 错误
default:
    return 0.0;  // 无效操作符不应该返回0
```

### 错误3：不必要的try-catch
```java
// ❌ 错误
default:
    try {
        throw new InvalidOperatorException("错误");
    } catch (InvalidOperatorException e) {
        throw e;  // 多余的捕获和重新抛出
    }
```

### 错误4：捕获不会发生的异常
```java
// ❌ 错误
default:
    try {
        return someValue;
    } catch (ArithmeticException e) {  // 这里不会有算术异常
        throw new InvalidOperatorException("错误");
    }
```

## 🛠️ 完整的正确实现

### Calculator.java的完整calculate方法：
```java
public double calculate(double a, double b, String operator) 
        throws DivideByZeroException, InvalidOperatorException {
    
    // 检查null输入
    if (operator == null) {
        throw new InvalidOperatorException("操作符不能为空");
    }
    
    // 处理各种操作符
    switch (operator) {
        case "+":
            return add(a, b);
        case "-":
            return subtract(a, b);
        case "*":
            return multiply(a, b);
        case "/":
            return divide(a, b);  // 可能抛出DivideByZeroException
        default:
            throw new InvalidOperatorException(
                "不支持的操作符：'" + operator + "'. 支持的操作符：+, -, *, /"
            );
    }
}
```

### 测试Default分支：
```java
// 测试无效操作符
try {
    double result = calc.calculate(10, 5, "%");
    System.out.println("结果：" + result);  // 不会执行
} catch (InvalidOperatorException e) {
    System.err.println("操作符错误：" + e.getMessage());
    // 输出：操作符错误：不支持的操作符：'%'. 支持的操作符：+, -, *, /
}

// 测试null操作符
try {
    double result = calc.calculate(10, 5, null);
    System.out.println("结果：" + result);  // 不会执行
} catch (InvalidOperatorException e) {
    System.err.println("操作符错误：" + e.getMessage());
    // 输出：操作符错误：操作符不能为空
}
```

## 🎯 关键要点总结

### 1. Default分支必须处理返回值
- 如果方法有返回值，default必须return或throw
- 不能使用空的`return;`

### 2. 异常处理要简洁
- 直接抛出异常，不需要try-catch包装
- 除非有特殊的日志记录需求

### 3. 错误信息要有用
- 告诉用户什么是错误的
- 提示用户什么是正确的

### 4. 输入验证要全面
- 检查null值
- 检查无效值
- 提供清晰的错误提示

## 🏆 最终建议

**你的default分支应该这样写：**
```java
default:
    throw new InvalidOperatorException("不支持的操作符：" + operator);
```

**就这么简单！不需要try-catch，不需要空return，直接抛出异常即可！** 🚀

package Collection;

import java.util.*;
import java.util.stream.Collectors;

public class Ex1 {
    public static void main(String[] args) {
        //多态经典   子类都实现了父类    接口引用指向实现对象
        //可以随时切换实现类     符合面向对象的编程
        Set<String> set = new HashSet<>();

        System.out.println("判断是否为空  set.isEmpty():"+set.isEmpty());

        set.add("a");
        set.add("b");
        set.add("c");
        //删除指定元素
        set.remove("a");


        System.out.println("判断是否有某个元素 set.contains(\"b\"):"+set.contains("b"));

        //获取长度
        System.out.println(set.size());
        System.out.println(set);

        Object[] a = set.toArray();

        System.out.println("转换为数组 Object[] a = set.toArray(); :");
        for (Object o : a) {
            System.out.print(o+" ");
        }
        System.out.println();

        set.clear();
        System.out.println("清空集合的内容clear():" +set.isEmpty());


        System.out.println("=====================Map=============================");
        //键值对  键重复的会将值替换掉
        Map<String, Integer> map = new HashMap<>();
        map.put("a", 1);
        map.put("b", 2);
        map.put("a", 3);
        map.put("c", 4);



        map.remove("c");

        System.out.println(map);


        System.out.println("根据键获取值 map.get(\"a\") :" + map.get("a"));

        System.out.println("获取集合的长度 map.size()  :" + map.size());

        //保存键
        Set<String> set1 = new HashSet<>();
        set1 = map.keySet();

        //保存值
        Collection<Integer> set2 = new HashSet<>();
        set2 = map.values();




        //将键值 转换成 set  用于遍历
        Set<Map.Entry<String, Integer>> set3 = map.entrySet();

        for(Map.Entry<String, Integer> i : map.entrySet()){
            System.out.println(i.getKey() + " : " + i.getValue());
        }


        //lambda  表达式遍历
        map.forEach((k,v)->{
            System.out.println(k+":"+v);
        });

        //通过键得到值
        for(String key : map.keySet()){
            System.out.println(map.get(key));
        }

        System.out.println("=====================list=============================");
        List<String> list = new ArrayList<>();


        list.add("a");
        list.add("b");
        list.add("c");

        list.remove("a");

        System.out.println(list);

        System.out.println("集合的长度list.size(): "+ list.size());


        set.addAll(map.keySet());


        System.out.println("=====================stream=====================");

        //list   有序 可重复
        List<String> names = Arrays.asList(
                "张三", "张三", "李四", "王五", "张六", "赵七", "钱八", "孙九", "周十",
                "张伟", "李娜", "王芳", "刘洋", "陈静", "杨帆", "黄磊", "周杰",
                "张小明", "李小红", "王大力", "刘小华", "陈美丽", "杨志强", "黄小燕", "周大伟",
                "张三丰", "李时珍", "王阳明", "刘备", "关羽", "张飞", "赵云", "马超"
        );

        List<String> zhangName = new ArrayList<>();
        // filter 文本过滤      .collect  收集到容器    skip 跳过第几个
        zhangName = names.stream().filter(s -> s.startsWith("张") &&(s.length()==2)).collect(Collectors.toList());

        System.out.println(zhangName);


    }
}

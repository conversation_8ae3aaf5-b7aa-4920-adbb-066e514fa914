# Day01 Java面向对象编程核心总结

## 📚 学习目标

通过day01-Oop-Code的学习，掌握Java面向对象编程的两大核心特性：
1. **Static 静态特性** - 理解类级别的成员变量和方法
2. **继承机制 (Inheritance)** - 实现代码复用和类的层次结构

## 🔧 Static静态特性 - 核心要点

### 1. 内存共享原理

**核心概念**：静态成员在内存中只有一份，被所有对象共享

```java
// Student.java - d1_static_field
public class Student {
    static String name;  // 静态变量：内存中只有一份，所有对象共享
    int age;            // 实例变量：每个对象独有
}
```

**实际验证**：
```java
// Test.java - d1_static_field
Student.name = "张三";        // 推荐：类名访问
Student s1 = new Student();
System.out.println(s1.name); // 输出：张三

s1.name = "马东梅";           // 不推荐：对象访问
Student s2 = new Student();
System.out.println(s2.name); // 输出：马东梅（最后一次修改影响所有）
```

**关键理解**：静态变量属于类，最后一次修改影响所有访问。

### 2. 访问规则核心

**核心规则**：静态只能访问静态，实例可以访问所有

```java
// Test.java - d3_static_attention
public class Test {
    public static String schoolName = "黑马程序员";
    private String name = "张三";
    
    // 静态方法只能直接访问静态成员
    public static void testStaticMethod(){
        // ✅ 可以访问静态变量
        System.out.println("静态变量: " + schoolName);
        
        // ❌ 不能直接访问实例变量（编译错误）
        // System.out.println(name);  
        
        // ❌ 不能使用this关键字（编译错误）
        // System.out.println(this.name);
        
        // ✅ 要访问实例成员，必须创建对象
        Test obj = new Test();
        System.out.println("通过对象访问: " + obj.name);
    }
    
    // 实例方法可以访问所有成员
    public void testInstanceMethod(){
        System.out.println("静态变量: " + schoolName); // ✅
        System.out.println("实例变量: " + name);       // ✅
        System.out.println("使用this: " + this.name); // ✅
    }
}
```

### 3. 工具类标准设计

**设计要点**：私有构造器 + 静态方法 = 标准工具类模式

```java
// IteimaUtil.java - d4_static_util
public class IteimaUtil {
    // 私有构造器防止实例化
    private IteimaUtil() {}
    
    // 静态工具方法
    public static String createCode(int cnt){
        String data = "ABCDEFGHIJKLMNOPQRSRUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        String code = "";
        Random random = new Random();
        for(int i = 0; i < cnt; i++){
            int index = random.nextInt(data.length());
            code += data.charAt(index);
        }
        return code;
    }
}
```

**使用方式**：
```java
// Login.java - d4_static_util
String code = IteimaUtil.createCode(5);  // 直接通过类名调用
// IteimaUtil util = new IteimaUtil();   // 编译错误：构造器私有
```

### 4. 单例模式实现

**饿汉式（立即创建）**：

```java
// A.java - d6_static_singleinstance
public class A {
    // 类加载时立即创建对象
    private static A a = new A();
    
    // 私有构造器
    private A(){}
    
    // 提供公共访问方法
    public static A getInstance(){
        return a;
    }
}
```

**懒汉式（延迟创建）**：
```java
// AA.java - d6_static_singleinstance
public class AA {
    private static AA a;
    
    private AA(){}
    
    public static AA getInstance(){
        // 第一次访问时才创建对象
        if(a == null){
            a = new AA();
        }
        return a;
    }
}
```

### 5. 常量vs静态变量

```java
// Test.java - d3_static_attention
public class Test {
    // 静态变量（可以修改）
    public static String schoolName = "黑马程序员";
    
    // 常量（不可修改）
    public static final String SCHOOL_TYPE = "IT培训机构";
    
    public static void main(String[] args) {
        // 静态变量可以修改
        schoolName = "新东方";  // ✅ 可以修改
        
        // 常量不能修改
        // SCHOOL_TYPE = "其他机构";  // ❌ 编译错误！
    }
}
```

**重要区别**：只有`public static final`才是真正的常量。

### 6. 静态代码块应用

```java
// CodeTest1.java - d5_static_code
public class CodeTest1 {
    public static String schoolName = "黑马";
    public static ArrayList<String> names = new ArrayList<>();
    
    // 静态代码块：类加载时执行一次，用于初始化静态资源
    static{
        System.out.println("----static静态代码执行----");
        names.add("张三");
        names.add("李四");
    }
}
```

**执行时机**：类第一次被加载时执行，且只执行一次。

## 🔗 继承机制 - 核心要点

### 1. 基础继承特点

```java
// A.java - d7_extends (父类)
public class A {
    public int i;           // 公共成员
    private int j;          // 私有成员
    
    public void print1(){
        System.out.println(i);
    }
    
    private void print2(){
        System.out.println(j);
    }
}

// B.java - d7_extends (子类)
public class B extends A {
    private int k;
    
    public void print3(){
        // ✅ 可以继承父类非私有成员
        print1();
        System.out.println(i);
        
        // ❌ 不能访问父类私有成员
        // print2();  // 编译错误
        // System.out.println(j);  // 编译错误
    }
}
```

**继承特点**：子类只能继承父类的非私有成员。

### 2. 访问修饰符控制

```java
// Fu.java - d9_extend_modifier
public class Fu {
    private void privateMethod() {}      // 子类不能访问
    void Method(){}                     // 同包子类可访问  
    protected void protectedMethod(){}   // 所有子类可访问
    public void publicMethod(){}         // 完全开放
}
```

**访问层次**：private < 默认 < protected < public

**跨包继承测试**：
```java
// Zi.java - d9_extend_modifier2 (不同包)
public class Zi extends Fu {
    public void print() {
        // Method();          // ❌ 默认访问权限，跨包不可访问
        protectedMethod();    // ✅ protected，子类可访问
        publicMethod();       // ✅ public，完全开放
        // privateMethod();   // ❌ private，子类不可访问
    }
}
```

### 3. 方法重写规范

```java
// Test2.java - d14_extend_files
class Zi2 extends Fu2{
    @Override  // 必须加注解，防止拼写错误
    public void run() {
        System.out.println("子类run");
    }
    
    public void go() {
        super.run();  // 调用父类方法
    }
}

class Fu2{
    public void run(){
        System.out.println("父类run");
    }
}
```

**重写要求**：
- 必须使用@Override注解
- 方法名、参数列表必须与父类一致
- 访问权限不能比父类更严格

### 4. 构造器调用链

```java
// Animal.java - d15_extends_constructor (父类)
public class Animal {
    public Animal() {
        System.out.println("调用Animal的无参构造");
    }
}

// Wolf.java - d15_extends_constructor (子类)
public class Wolf extends Animal {
    public Wolf() {
        super();  // 默认调用，写不写都存在
        System.out.println("调用wolf的无参构造");
    }
    
    public Wolf(String name) {
        // super();  // 默认存在
        System.out.println("调用wolf的有参构造");
    }
}
```

**执行顺序**：先调用父类构造器，再调用子类构造器。

### 5. 成员访问就近原则

```java
// Test.java - d14_extend_files
class Zi extends Fu{
    String name = "子类名称";
    
    public void showName(){
        String name = "局部名称";           // 局部变量优先级最高
        System.out.println(name);          // 输出：局部名称
        System.out.println(this.name);     // 输出：子类名称
        System.out.println(super.name);    // 输出：父类名称
    }
}

class Fu{
    String name = "父类名称";
}
```

**访问优先级**：局部变量 > 实例变量 > 父类变量

### 6. 继承特性限制

```java
// Test.java - d11_extends_feature
// ❌ Java不支持多继承
// class C extends A,B{}  // 编译错误

// ✅ 支持多层继承
class M extends Object{}  // 所有类默认继承Object
class N extends M{}
class P extends N{}
```

**继承规则**：
- 只支持单继承，不支持多继承
- 支持多层继承
- 所有类默认继承Object类

## 💡 实践应用总结

### Static适用场景
- **工具类方法**：Math.max()、Arrays.sort()
- **计数器功能**：统计对象创建数量
- **单例模式**：数据库连接池、配置管理器
- **常量定义**：错误码、状态码、配置信息

### 继承适用场景  
- **代码复用**：Employee → Programmer、Manager
- **建立类型关系**：Animal → Dog、Cat
- **多态基础**：父类引用指向子类对象
- **框架设计**：BaseDAO → UserDAO、OrderDAO

### 常见错误避免
1. ❌ 不要用对象调用静态方法
2. ❌ 静态方法中不能直接访问实例成员
3. ❌ 重写方法忘记加@Override注解
4. ❌ 子类重写方法的访问权限比父类更严格
5. ❌ 构造器中super()和this()同时出现

## 🎯 学习检验

通过day01的学习，您应该能够：
1. 理解静态成员的内存模型和访问规则
2. 设计标准的工具类和单例模式
3. 正确使用继承实现代码复用
4. 掌握方法重写和访问控制
5. 理解构造器继承和成员访问原则

这些概念是Java面向对象编程的基础，为后续学习多态、抽象类、接口等高级特性打下坚实基础。

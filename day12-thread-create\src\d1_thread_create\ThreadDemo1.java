package d1_thread_create;



public class ThreadDemo1 {
    public static void main(String[] args) {
        // 3. 创建线程对象 代表具体的线程
        Thread t = new MyThread();
        // 4.启动线程  会自动执行run方法
        t.start();
        //不能直接调用run  cpu不会注册新线程执行 此时相当于单线程

        // 主线程执行的任务    主线程要放在启动子线程之后      否则相当于单线程
        for (int i = 0; i < 5; i++) {
            System.out.println("主线程输出：" + i);
        }
    }
}

//1.定义一个为继承thread类，成为线程类
class MyThread extends Thread {
    //2.重写run方法，声明线程要干的事
    @Override
    public void run() {
        for (int i = 0; i < 5; i++) {
            System.out.println("子线程输出：" + i);
        }
    }
}
package exercise6;

import java.util.*;

/**
 * 练习6：扑克牌组类
 * 
 * 要求：
 * 1. 包含52张牌的完整牌组
 * 2. 实现洗牌、发牌、重置功能
 * 3. 内部类CardComparator实现牌的比较
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class Deck {
    
    // TODO: 定义牌组列表
    List<Card> cards = new ArrayList<>();


    // TODO: 构造方法（初始化54张牌）
    public Deck() {
        // 你的实现
        // 需要创建所有花色和点数的组合

        for(Rank rank : Rank.values()){
            if(rank == Rank.SMALL_JOKER||rank==Rank.BIG_JOKER){
                cards.add(new Card(rank,null));
            }
            else{
                for(Suit suit : Suit.values()){
                    cards.add(new Card(rank,suit));
                }
            }
        }
    }

    // TODO: 洗牌方法
    public void shuffle() {
        // 你的实现
        // 提示：可以使用Collections.shuffle()
        Collections.shuffle(cards);
    }

    // TODO: 发一张牌
    public Card deal() {
        // 你的实现
        // 注意：需要检查牌组是否为空
        Card card = cards.get(0);
        cards.remove(card);
        return card;
    }

    // TODO: 重置牌组
    public void reset() {
        // 你的实现
        cards.clear();
    }

    // TODO: 获取剩余牌数
    public int remainingCards() {
        // 你的实现
        return cards.size();
    }

    // TODO: 判断牌组是否为空
    public boolean isEmpty() {
        // 你的实现
        return cards.isEmpty();
    }

    // TODO: 按点数排序牌组
    public void sortByValue() {
        // 你的实现
        // 使用内部类CardComparator
        cards.sort(Comparator.comparingInt(Card::getValue));
    }

    // TODO: 显示所有牌
    public void displayAllCards() {
        // 你的实现
        cards.stream().forEach(card -> {
            if(card.suit == null){
                System.out.println(card.rank.getSymbol());
            }
            else{
                System.out.println(card.suit.getSuit()+card.rank.getSymbol());
            }
        });
    }

    // TODO: 内部类：牌的比较器
    private class CardComparator implements Comparator<Card> {
        @Override
        public int compare(Card c1, Card c2) {
            // 你的实现
            // 先按点数比较，再按花色比较
            int rankComparison = c1.getRank().compareTo(c2.getRank());
            if(rankComparison==0){
                return c1.getSuit().compareTo(c2.getSuit());
            }
            return rankComparison;
        }
    }

    // TODO: toString方法

    @Override
    public String toString() {
        return "Deck{" +
                "cards=" + cards +
                '}';
    }
}

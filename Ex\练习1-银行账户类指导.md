# 练习1：银行账户类 - 实现指导 🏦

## 🎯 练习目标
通过实现银行账户类，掌握Java面向对象编程的基础概念：封装、构造方法、方法设计等。

## 📋 具体要求

### 类设计要求
**BankAccount类需要包含：**

#### 属性设计
```java
// 提示：考虑访问修饰符的选择
private String accountNumber;  // 账户号码
private double balance;        // 账户余额  
private String holderName;     // 账户持有人姓名
```

#### 构造方法
```java
// 提示：需要初始化所有必要的属性
public BankAccount(String accountNumber, String holderName, double initialBalance) {
    // 你的实现
}
```

#### 核心方法
1. **存款方法**
   - 参数验证（金额必须大于0）
   - 更新余额
   - 输出操作结果

2. **取款方法**  
   - 参数验证（金额必须大于0）
   - 余额检查（余额不足时提示）
   - 更新余额
   - 返回操作是否成功

3. **余额查询**
   - 返回当前余额

4. **信息显示**
   - 格式化输出账户信息

## 💡 实现提示

### 存款方法实现思路
```java
public void deposit(double amount) {
    // 1. 检查金额是否有效（大于0）
    // 2. 如果有效，增加余额
    // 3. 输出操作成功信息
    // 4. 如果无效，输出错误信息
}
```

### 取款方法实现思路
```java
public boolean withdraw(double amount) {
    // 1. 检查金额是否有效（大于0）
    // 2. 检查余额是否充足
    // 3. 如果都满足，扣除余额，返回true
    // 4. 否则输出相应错误信息，返回false
}
```

### toString方法实现思路
```java
@Override
public String toString() {
    // 返回格式化的账户信息字符串
    // 例如："账户号码：123456，持有人：张三，余额：1000.00元"
}
```

## 🧪 测试用例设计

### 基本功能测试
```java
// 1. 创建账户
BankAccount account1 = new BankAccount("123456", "张三", 1000.0);
BankAccount account2 = new BankAccount("789012", "李四", 500.0);

// 2. 存款测试
account1.deposit(500);    // 正常存款
account1.deposit(-100);   // 无效金额测试

// 3. 取款测试  
account1.withdraw(200);   // 正常取款
account1.withdraw(2000);  // 余额不足测试
account1.withdraw(-50);   // 无效金额测试

// 4. 余额查询
System.out.println("当前余额：" + account1.getBalance());

// 5. 账户信息显示
System.out.println(account1.toString());
```

### 边界情况测试
- 初始余额为0的账户
- 存款金额为0或负数
- 取款金额超过余额
- 取款金额为0或负数

## ✅ 完成检查清单

### 代码实现
- [ ] 定义了所有必需的属性
- [ ] 实现了构造方法
- [ ] 实现了deposit方法（包含参数验证）
- [ ] 实现了withdraw方法（包含余额检查）
- [ ] 实现了getBalance方法
- [ ] 实现了toString方法
- [ ] 添加了适当的访问修饰符

### 功能测试
- [ ] 能够创建账户对象
- [ ] 存款功能正常工作
- [ ] 取款功能正常工作（包括余额不足处理）
- [ ] 余额查询正确
- [ ] 账户信息显示格式正确
- [ ] 异常情况处理得当

### 代码质量
- [ ] 代码格式规范
- [ ] 变量命名清晰
- [ ] 添加了必要的注释
- [ ] 遵循Java编码规范

## 🚀 运行测试

### 编译命令
```bash
cd Ex
javac src/exercise1/*.java
```

### 运行命令
```bash
java exercise1.BankAccountTest
```

### 预期输出示例
```
=== 银行账户管理系统测试 ===

--- 存款测试 ---
张三账户存款500.0元成功，当前余额：1500.0元
存款金额必须大于0

--- 取款测试 ---
张三账户取款200.0元成功，当前余额：1300.0元
余额不足，无法取款2000.0元，当前余额：1300.0元

--- 余额查询 ---
张三当前余额：1300.0元

--- 最终账户信息 ---
账户号码：123456，持有人：张三，余额：1300.00元

=== 测试完成 ===
```

## 🎓 知识点总结

完成这个练习后，你应该掌握：
- **封装原则**：使用private修饰属性，通过方法控制访问
- **构造方法**：对象初始化的标准方式
- **方法设计**：参数验证、返回值设计、异常情况处理
- **toString方法**：对象信息的字符串表示
- **代码规范**：命名、格式、注释等

**完成后告诉我，我来帮你检查代码或解答疑问！** 💪

# Day03 Java面向对象编程高级特性总结

## 📚 学习目标

通过day03-oop-code的学习，掌握Java面向对象编程的高级特性：
1. **内部类 (Inner Classes)** - 四种内部类类型及应用场景
2. **枚举 (Enum)** - 枚举类型定义和实际应用
3. **泛型 (Generics)** - 泛型类、泛型接口的设计与使用
4. **Object类** - 核心方法重写和Objects工具类
5. **包装类 (Wrapper Classes)** - 基本类型与对象类型转换

## 🏠 内部类 - 核心要点

### 1. 成员内部类 (d1_innerclass1)

**核心概念**：
- 定义在外部类内部，没有static修饰
- 属于外部类的对象持有
- 可以直接访问外部类的所有成员（包括私有成员）

```java
// Outer.java - d1_innerclass1
public class Outer {
    public static String schoolName = "黑马";
    public static void inAddr() {
        System.out.println("我在黑马");
    }
    
    private String hobby;
    private double height;
    
    public void run() {
        System.out.println("run");
    }
    
    // 成员内部类
    public class Inner {
        private String name;
        private int age;
        
        public void show() {
            // 可以直接访问外部类的静态成员
            System.out.println(schoolName);
            inAddr();
            
            // 可以访问外部类的实例成员
            System.out.println(hobby);
            System.out.println(height);
            run();
        }
    }
}

// People.java - d1_innerclass1 (变量作用域演示)
public class People {
    private int hearBeat = 110;
    
    public class Heart {
        private int hearBeat = 95;
        
        public void show() {
            int hearBeat = 80;
            System.out.println(hearBeat);           // 80 (局部变量)
            System.out.println(this.hearBeat);     // 95 (内部类成员)
            System.out.println(People.this.hearBeat); // 110 (外部类成员)
        }
    }
}

// Test.java - d1_innerclass1
public class Test {
    public static void main(String[] args) {
        // 成员内部类创建对象的语法
        // 外部类名.内部类名 对象名 = new 外部类名().new 内部类名()
        Outer.Inner inner1 = new Outer().new Inner();
        inner1.setAge(30);
        inner1.setName("s");
        inner1.show();
        
        People.Heart people = new People().new Heart();
        people.show();
    }
}
```

**运行结果**：
```
黑马
我在黑马
null
0.0
run
80
95
110
```

### 2. 静态内部类 (d2_innerclass2)

**核心概念**：
- 有static修饰，属于外部类本身所有
- 只能直接访问外部类的静态成员
- 访问外部类实例成员需要创建外部类对象

```java
// Outer.java - d2_innerclass2
public class Outer {
    public static String schoolName = "黑马";
    public static void inAddr() {
        System.out.println("我在黑马");
    }
    
    private double height;
    
    // 静态内部类
    public static class Inner {
        private String name;
        private int age;
        
        public void show() {
            // 可以直接访问外部类的静态成员
            System.out.println(schoolName);
            inAddr();
            
            // 不能直接访问外部类的实例成员
            // System.out.println(height); // 编译错误
            
            // 需要通过外部类对象访问实例成员
            // Outer o = new Outer();
            // System.out.println(o.height);
        }
    }
}

// Test.java - d2_innerclass2
public class Test {
    public static void main(String[] args) {
        // 静态内部类创建对象的语法
        // 外部类名.内部类名 对象名 = new 外部类名.静态内部类名()
        Outer.Inner inner = new Outer.Inner();
        inner.show();
    }
}
```

**运行结果**：
```
黑马
我在黑马
```

### 3. 局部内部类 (d3_innerclass3)

**核心概念**：
- 定义在方法、构造器、代码块等局部范围里的类
- 鸡肋语法，实际开发中很少使用

```java
// Test.java - d3_innerclass3
public class Test {
    public static void main(String[] args) {
        // 局部内部类定义在方法里
        class A {
            public void show() {
                // 方法体
            }
        }
    }
}
```

### 4. 匿名内部类 (d4_innerclass4)

**核心概念**：
- 本质是一个子类，同时会立即创建一个子类对象
- 简化代码，是新技术的基础
- 常用作方法参数传递

```java
// Test.java - d4_innerclass4
abstract class Animal {
    public abstract void cry();
}

public class Test {
    public static void main(String[] args) {
        // 匿名内部类
        Animal a = new Animal() {
            @Override
            public void cry() {
                System.out.println("狗叫");
            }
        };
        a.cry();
    }
}
```

**实际应用场景**：

```java
// Test2.java - d4_innerclass4_2
interface Swimming {
    void swim();
}

public class Test2 {
    public static void main(String[] args) {
        // 匿名内部类作为参数传递
        Swimming s1 = new Swimming() {
            @Override
            public void swim() {
                System.out.println("学生在喝水");
            }
        };
        go(s1);
        
        // 直接传递匿名内部类
        go(new Swimming() {
            @Override
            public void swim() {
                System.out.println("老师沉底了");
            }
        });
    }
    
    public static void go(Swimming s) {
        System.out.println("开始");
        s.swim();
        System.out.println("结束\n");
    }
}
```

**运行结果**：
```
狗叫
开始
学生在喝水
结束

开始
老师沉底了
结束
```

## 🏷️ 枚举 - 核心要点

### 1. 枚举基础 (d5_enum)

**核心概念**：
- 枚举类不能对外创建对象，不能被继承
- 第一行必须罗列枚举对象的名称
- 枚举对象本质是常量对象

```java
// A.java - d5_enum
public enum A {
    // 枚举第一行必须是罗列的枚举对象名称
    X, Y, Z;
    
    private String name;
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
}

// Test.java - d5_enum
public class Test {
    public static void main(String[] args) {
        A a1 = A.X;
        A a2 = A.Y;
        
        // 遍历所有枚举值
        A[] as = A.values();
        for (A a : as) {
            System.out.println(a);
        }
        
        // ordinal() 获取对象的索引位置
        System.out.println(a1.ordinal()); // 0
        System.out.println(a2.ordinal()); // 1
    }
}
```

**运行结果**：
```
X
Y
Z
0
1
```

### 2. 枚举应用场景 (d6_enum2)

**核心概念**：
- 枚举做信息标识和分类，参数值能够约束
- 相比常量类，枚举提供了类型安全

```java
// Constant.java - d6_enum2 (传统常量方式)
public class Constant {
    public static final int DOWN = 1;
    public static final int UP = 2;
    public static final int HALF_UP = 3;
    public static final int DELETE = 4;
}

// Constant2.java - d6_enum2 (枚举方式)
public enum Constant2 {
    DOWN, UP, HALF, DELETE
}

// Test.java - d6_enum2 (常量方式)
public class Test {
    public static void main(String[] args) {
        // 常量作信息标识，但参数值没有进行约束
        System.out.println(handleData(3.1, Constant.DELETE));
    }
    
    public static double handleData(double number, int type) {
        switch (type) {
            case Constant.DOWN:
                number = Math.floor(number);  // 向下取整
                break;
            case Constant.UP:
                number = Math.ceil(number);   // 向上取整
                break;
            case Constant.HALF_UP:
                number = Math.round(number);  // 四舍五入
                break;
            case Constant.DELETE:
                number = (int)(number);       // 去掉小数
                break;
        }
        return number;
    }
}

// Test1.java - d6_enum2 (枚举方式)
public class Test1 {
    public static void main(String[] args) {
        // 枚举做信息标识和分类，参数值能够约束
        System.out.println(handleData(3.1, Constant2.DELETE));
    }
    
    public static double handleData(double number, Constant2 type) {
        switch (type) {
            case DOWN:
                number = Math.floor(number);
                break;
            case UP:
                number = Math.ceil(number);
                break;
            case HALF:
                number = Math.round(number);
                break;
            case DELETE:
                number = (int)(number);
                break;
        }
        return number;
    }
}
```

**运行结果**：
```
3.0
```

**枚举单例模式**：
```java
// B.java - d5_enum
public enum B {
    X; // 单例模式的枚举实现
}
```

## 🔧 泛型 - 核心要点

### 1. 泛型基础 (d7_genericity)

**核心概念**：
- 定义类、接口、方法时，同时声明一个或多个类型变量如<E>
- 本质是把具体的数据类型作为参数传给类型变量
- 提供编译时类型安全检查

```java
// Test.java - d7_genericity
public class Test {
    public static void main(String[] args) {
        // 泛型的使用
        ArrayList<String> list = new ArrayList<>();
        list.add("asdfa");

        // 开发需要统一数据类型
        for (int i = 0; i < list.size(); i++) {
            String ele = list.get(i);
            System.out.println(ele);
        }
    }
}
```

**运行结果**：
```
asdfa
```

### 2. 泛型类 (d8_genericiry_class)

**核心概念**：
- 自定义泛型类，提供类型安全的容器
- 类型参数在类名后声明

```java
// MyArrayList.java - d8_genericiry_class
public class MyArrayList<E> {
    private ArrayList list = new ArrayList();

    public boolean add(E e) {
        list.add(e);
        return true;
    }

    public boolean remove(E e) {
        return list.remove(e);
    }

    @Override
    public String toString() {
        return list.toString();
    }
}

// Test.java - d8_genericiry_class
public class Test {
    public static void main(String[] args) {
        // 使用自定义泛型类
        MyArrayList<String> list = new MyArrayList<>();

        list.add("11");
        list.add("22");

        list.remove("22");

        System.out.println(list);
    }
}
```

**运行结果**：
```
[11]
```

### 3. 泛型接口 (d9_genericity_interface)

**核心概念**：
- 接口可以定义泛型，实现类可以指定具体类型
- 提供统一的数据操作规范

```java
// Date.java - d9_genericity_interface
public interface Date<E> {
    void add(E e);
    void delete(E e);
    void update(E e);
    E getById(int id);
}

// Student.java - d9_genericity_interface
public class Student {
    // 学生类定义
}

// Teacher.java - d9_genericity_interface
public class Teacher {
    // 教师类定义
}

// StudentDateImpl.java - d9_genericity_interface
public class StudentDateImpl implements Date<Student> {
    @Override
    public void add(Student student) {
        // 学生数据添加逻辑
    }

    @Override
    public void delete(Student student) {
        // 学生数据删除逻辑
    }

    @Override
    public void update(Student student) {
        // 学生数据更新逻辑
    }

    @Override
    public Student getById(int id) {
        return null;
    }
}

// TeacherDataImpl.java - d9_genericity_interface
public class TeacherDataImpl implements Date {
    // 未指定泛型类型，使用Object
    @Override
    public void add(Object o) {
        // 教师数据添加逻辑
    }

    @Override
    public void delete(Object o) {
        // 教师数据删除逻辑
    }

    @Override
    public void update(Object o) {
        // 教师数据更新逻辑
    }

    @Override
    public Object getById(int id) {
        return null;
    }
}
```

## 🎯 Object类 - 核心要点

### 1. Object类方法重写 (d11_Object)

**核心概念**：
- 所有类都默认继承Object类
- toString()和equals()方法通常需要重写
- 提供对象的字符串表示和相等性比较

```java
// Student.java - d11_Object
public class Student {
    private String name;
    private int age;
    private double score;

    public Student() {}

    public Student(String name, int age, double score) {
        this.name = name;
        this.age = age;
        this.score = score;
    }

    // 重写toString方法
    @Override
    public String toString() {
        return "Student{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", score=" + score +
                '}';
    }

    // 重写equals方法
    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        Student student = (Student) o;
        return age == student.age &&
               Double.compare(score, student.score) == 0 &&
               Objects.equals(name, student.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, age, score);
    }
}

// Test.java - d11_Object
public class Test {
    public static void main(String[] args) {
        Student s1 = new Student("1", 12, 100);

        // toString默认返回对象的地址形式，重写后返回内容
        System.out.println(s1.toString());
        System.out.println(s1);

        // equals的意义是让子类重写，以便自己制定比较规则
        Student t1 = new Student("师太", 35, 98);
        Student t2 = new Student("师太", 35, 98);
        System.out.println(t1.equals(t2)); // true (内容相同)
        System.out.println(t1 == t2);      // false (地址不同)
    }
}
```

**运行结果**：
```
Student{name='1', age=12, score=100.0}
Student{name='1', age=12, score=100.0}
true
false
```

### 2. Objects工具类 (d12_Objects)

**核心概念**：
- Objects类提供了更安全的对象操作方法
- 避免空指针异常

```java
// Test.java - d12_Objects
public class Test {
    public static void main(String[] args) {
        Student s1 = null;
        Student s2 = new Student("2", 12, 100);

        // 直接调用会抛出空指针异常
        // System.out.println(s1.equals(s2)); // NullPointerException

        // Objects.equals()更安全可靠
        System.out.println(Objects.equals(s1, s2)); // false
    }
}
```

**运行结果**：
```
false
```

## 📦 包装类 - 核心要点

### 1. 包装类基础 (d13interrger)

**核心概念**：
- 把基本数据类型包装成对象
- 提供自动装箱和拆箱机制
- 支持基本类型与字符串的相互转换

```java
// Testt.java - d13interrger
public class Testt {
    public static void main(String[] args) {
        int a = 12;

        // 1. 手动包装
        Integer b = Integer.valueOf(a);
        System.out.println(b);

        // 2. 自动装箱机制
        Integer it2 = 128;
        Integer it3 = 128;
        System.out.println(it2 == it3); // false (超出缓存范围)

        // 3. 自动拆箱
        int c = it2;
        System.out.println(c);

        System.out.println("------------------------------------");
        System.out.println("包装类的功能");

        // 功能一：包装类可以把基本数据类型转换成字符串
        Integer it4 = 123;
        String str = it4.toString();
        System.out.println(str + 1); // "1231"

        int a1 = 123;
        String rs3 = a1 + "";
        System.out.println(rs3 + 1); // "1231"

        // 功能二：把字符串数值转换成基本数据类型
        String str1 = "123";
        int num = Integer.parseInt(str1);
        System.out.println(num + 1); // 124

        String str2 = "99.5";
        double score = Double.parseDouble(str2);
        System.out.println(score); // 99.5

        // 功能三：泛型和集合都不支持基本数据类型，因此包装类在集合与泛型中大量使用
    }
}
```

**运行结果**：
```
12
false
128
------------------------------------
包装类的功能
1231
1231
124
99.5
```

## 💡 实践应用总结

### 内部类适用场景
- **成员内部类**：需要访问外部类所有成员的场景
- **静态内部类**：只需要访问外部类静态成员的工具类
- **匿名内部类**：事件处理、回调函数、简化代码

### 枚举适用场景
- **常量定义**：状态码、配置选项等固定值集合
- **类型安全**：替代传统常量类，提供编译时检查
- **单例模式**：线程安全的单例实现

### 泛型适用场景
- **集合框架**：ArrayList<T>、HashMap<K,V>等
- **工具类设计**：提供类型安全的通用功能
- **API设计**：增强代码可读性和类型安全

### Object类和包装类适用场景
- **toString重写**：对象调试和日志输出
- **equals重写**：对象内容比较
- **包装类**：基本类型与对象类型转换、集合存储

## 🎯 学习检验

通过day03的学习，您应该能够：
1. 熟练使用四种内部类，理解各自的特点和应用场景
2. 设计和使用枚举类型，实现类型安全的常量管理
3. 创建泛型类和泛型接口，提供类型安全的通用功能
4. 正确重写Object类的核心方法，使用Objects工具类
5. 理解包装类的特性，熟练进行基本类型与对象类型转换

这些高级特性是Java面向对象编程的重要组成部分，为后续学习集合框架、反射、注解等高级内容奠定基础。

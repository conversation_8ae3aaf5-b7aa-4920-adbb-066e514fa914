# 练习6：包含大小王的扑克牌设计方案 🃏

## 🎯 需求澄清

**用户要求**：包含大小王，点数排序为：3-K, A, 2, 小王, 大王

**牌组总数**：54张牌（52张标准牌 + 2张王牌）

## 🃏 修正后的设计方案

### 1. Rank枚举设计（包含大小王）

```java
public enum Rank {
    THREE("3", 3),
    FOUR("4", 4),
    FIVE("5", 5),
    SIX("6", 6),
    SEVEN("7", 7),
    EIGHT("8", 8),
    NINE("9", 9),
    TEN("10", 10),
    JACK("J", 11),
    QUEEN("Q", 12),
    KING("K", 13),
    ACE("A", 14),           // A在K之后
    TWO("2", 15),           // 2在A之后
    SMALL_JOKER("小王", 16), // 小王
    BIG_JOKER("大王", 17);   // 大王
    
    private final String symbol;
    private final int value;
    
    Rank(String symbol, int value) {
        this.symbol = symbol;
        this.value = value;
    }
    
    public String getSymbol() { return symbol; }
    public int getValue() { return value; }
    
    // 判断是否为王牌
    public boolean isJoker() {
        return this == SMALL_JOKER || this == BIG_JOKER;
    }
    
    // 判断是否为人头牌
    public boolean isFaceCard() {
        return this == JACK || this == QUEEN || this == KING;
    }
    
    @Override
    public String toString() {
        return symbol;
    }
}
```

### 2. Suit枚举设计（王牌特殊处理）

```java
public enum Suit {
    HEARTS("红桃", "♥"),
    SPADES("黑桃", "♠"), 
    DIAMONDS("方块", "♦"),
    CLUBS("梅花", "♣"),
    JOKER("王", "🃏");      // 王牌花色
    
    private final String chineseName;
    private final String symbol;
    
    Suit(String chineseName, String symbol) {
        this.chineseName = chineseName;
        this.symbol = symbol;
    }
    
    public String getChineseName() { return chineseName; }
    public String getSymbol() { return symbol; }
    
    // 判断颜色（王牌为特殊颜色）
    public boolean isRed() {
        return this == HEARTS || this == DIAMONDS;
    }
    
    public boolean isBlack() {
        return this == SPADES || this == CLUBS;
    }
    
    public boolean isJoker() {
        return this == JOKER;
    }
    
    @Override
    public String toString() {
        return symbol + chineseName;
    }
}
```

### 3. Card类设计（使用枚举）

```java
public class Card {
    private final Suit suit;   // 使用枚举
    private final Rank rank;   // 使用枚举
    
    public Card(Suit suit, Rank rank) {
        this.suit = suit;
        this.rank = rank;
    }
    
    public Suit getSuit() { return suit; }
    public Rank getRank() { return rank; }
    
    // 获取牌的点数值
    public int getValue() {
        return rank.getValue();
    }
    
    // 判断是否为王牌
    public boolean isJoker() {
        return rank.isJoker();
    }
    
    // 判断是否为红色牌
    public boolean isRed() {
        if (isJoker()) {
            return rank == Rank.BIG_JOKER;  // 大王为红色
        }
        return suit.isRed();
    }
    
    // 判断是否为黑色牌
    public boolean isBlack() {
        if (isJoker()) {
            return rank == Rank.SMALL_JOKER;  // 小王为黑色
        }
        return suit.isBlack();
    }
    
    @Override
    public String toString() {
        if (isJoker()) {
            return rank.getSymbol();  // 王牌只显示点数
        }
        return suit.getSymbol() + rank.getSymbol();  // 普通牌显示花色+点数
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Card card = (Card) obj;
        return suit == card.suit && rank == card.rank;
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(suit, rank);
    }
}
```

### 4. Deck类设计（54张牌）

```java
public class Deck {
    private List<Card> cards;
    private int currentIndex;
    
    public Deck() {
        reset();
    }
    
    // 重置牌组（创建54张牌）
    public void reset() {
        cards = new ArrayList<>();
        currentIndex = 0;
        
        // 创建52张标准牌
        for (Suit suit : Suit.values()) {
            if (suit != Suit.JOKER) {  // 跳过王牌花色
                for (Rank rank : Rank.values()) {
                    if (!rank.isJoker()) {  // 跳过王牌点数
                        cards.add(new Card(suit, rank));
                    }
                }
            }
        }
        
        // 添加2张王牌
        cards.add(new Card(Suit.JOKER, Rank.SMALL_JOKER));  // 小王
        cards.add(new Card(Suit.JOKER, Rank.BIG_JOKER));    // 大王
    }
    
    // 洗牌
    public void shuffle() {
        Collections.shuffle(cards);
        currentIndex = 0;
    }
    
    // 发一张牌
    public Card deal() {
        if (isEmpty()) {
            throw new IllegalStateException("牌组已空，无法发牌");
        }
        return cards.get(currentIndex++);
    }
    
    // 判断是否还有牌
    public boolean isEmpty() {
        return currentIndex >= cards.size();
    }
    
    // 剩余牌数
    public int remainingCards() {
        return cards.size() - currentIndex;
    }
    
    // 按点数排序（3-K, A, 2, 小王, 大王）
    public void sortByValue() {
        List<Card> availableCards = cards.subList(currentIndex, cards.size());
        availableCards.sort(new CardComparator());
    }
    
    // 内部类：牌的比较器
    private class CardComparator implements Comparator<Card> {
        @Override
        public int compare(Card c1, Card c2) {
            // 先按点数比较
            int rankCompare = Integer.compare(c1.getRank().getValue(), 
                                            c2.getRank().getValue());
            if (rankCompare != 0) {
                return rankCompare;
            }
            
            // 点数相同则按花色比较（王牌除外）
            if (!c1.isJoker() && !c2.isJoker()) {
                return c1.getSuit().compareTo(c2.getSuit());
            }
            
            return 0;  // 王牌之间不再细分
        }
    }
    
    @Override
    public String toString() {
        return "Deck{总牌数=54, 剩余牌数=" + remainingCards() + "}";
    }
}
```

## 🎮 使用示例

```java
public class PokerGameTest {
    public static void main(String[] args) {
        // 创建54张牌的牌组
        Deck deck = new Deck();
        System.out.println("新牌组: " + deck);
        
        // 洗牌
        deck.shuffle();
        System.out.println("洗牌后: " + deck);
        
        // 发牌演示
        System.out.println("\n发牌演示:");
        for (int i = 0; i < 8; i++) {
            Card card = deck.deal();
            System.out.println("第" + (i+1) + "张牌: " + card + 
                             " (点数:" + card.getValue() + 
                             ", 类型:" + (card.isJoker() ? "王牌" : "普通牌") + ")");
        }
        
        // 点数排序演示
        System.out.println("\n点数排序（3-K, A, 2, 小王, 大王）:");
        for (Rank rank : Rank.values()) {
            System.out.println(rank + " (值:" + rank.getValue() + ")");
        }
    }
}
```

## 🔧 你当前代码的修复建议

### 1. 修复Rank枚举

```java
// ✅ 按照你的要求修复点数值
ACE("A", 14),           // 改为14（在K之后）
TWO("2", 15),           // 改为15（在A之后）
// 保留你的大小王设计
SMALL_JOKER("小王", 16),  // 重命名为标准格式
BIG_JOKER("大王", 17);    // 重命名为标准格式
```

### 2. 修复Card类使用枚举

```java
// ✅ 改为使用枚举属性
private final Suit suit;
private final Rank rank;

// ✅ 构造方法使用枚举参数
public Card(Suit suit, Rank rank) {
    this.suit = suit;
    this.rank = rank;
}
```

### 3. 实现Deck类创建54张牌

```java
public void reset() {
    cards = new ArrayList<>();
    
    // 创建52张标准牌
    for (Suit suit : Suit.values()) {
        if (suit != Suit.JOKER) {
            for (Rank rank : Rank.values()) {
                if (!rank.isJoker()) {
                    cards.add(new Card(suit, rank));
                }
            }
        }
    }
    
    // 添加2张王牌
    cards.add(new Card(Suit.JOKER, Rank.SMALL_JOKER));
    cards.add(new Card(Suit.JOKER, Rank.BIG_JOKER));
}
```

## 🎯 总结

**关键修复点：**
1. ✅ 保留大小王设计（符合你的需求）
2. ✅ 修正点数排序：3-K(3-13), A(14), 2(15), 小王(16), 大王(17)
3. ✅ Card类必须使用Suit和Rank枚举作为属性
4. ✅ Deck类创建54张牌（52张标准牌 + 2张王牌）

**这样设计既满足了你包含大小王的需求，又正确使用了枚举的核心概念！** 🃏✨

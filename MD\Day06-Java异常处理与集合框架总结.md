# Day06 Java异常处理与集合框架总结

## 📚 学习目标

通过day06-exception-collection的学习，掌握Java核心编程的两大重要特性：
1. **异常处理机制** - 程序健壮性的保障
2. **集合框架基础** - 数据结构的Java实现
3. **Collection接口** - 集合的统一操作规范
4. **List接口** - 有序可重复集合的实现
5. **Set接口** - 无序不重复集合的实现

## ⚠️ 异常处理机制 - 程序健壮性保障

### 核心概念
- 异常是程序运行时出现的错误或意外情况
- Java通过异常处理机制保证程序的健壮性
- 异常分为编译时异常(Checked Exception)和运行时异常(Runtime Exception)
- 异常处理的核心关键字：try、catch、finally、throw、throws

### 1. 异常处理基础语法 (ExceptionDemo1)

```java
public class ExceptionDemo1 {
    public static void main(String[] args) {
        System.out.println("===开始===");
        
        try {
            int a = 10;
            int b = 0;
            int c = a / b; // 可能出现异常的代码
            System.out.println(c);
        } catch (Exception e) {
            // 异常处理代码
            e.printStackTrace();
        }
        
        System.out.println("11");
        System.out.println("22");
        System.out.println("33");
        System.out.println("===结束===");
    }
}
```

**运行结果**：
```
===开始===
11
22
33
===结束===
```

### 2. 异常的抛出与传播 (ExceptionDemo2)

```java
public class ExceptionDemo2 {
    public static void main(String[] args) {
        System.out.println("参数存在问题");
        
        try {
            divide(10, 0);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    
    public static void divide(int a, int b) {
        if (b == 0) {
            throw new RuntimeException("/by 0");
        }
        int c = a / b;
        System.out.println(c);
    }
}
```

**运行结果**：
```
参数存在问题
Exception in thread "main" java.lang.RuntimeException: java.lang.RuntimeException: /by 0
        at d1_exception.ExceptionDemo2.main(ExceptionDemo2.java:18)
Caused by: java.lang.RuntimeException: /by 0
        at d1_exception.ExceptionDemo2.divide(ExceptionDemo2.java:25)
        at d1_exception.ExceptionDemo2.main(ExceptionDemo2.java:15)
```

### 3. 自定义异常 (ExceptionDemo3)

```java
// 自定义运行时异常
public class AgeIllegaRunTimeException extends RuntimeException {
    public AgeIllegaRunTimeException() {
    }

    public AgeIllegaRunTimeException(String message) {
        super(message);
    }
}

public class ExceptionDemo3 {
    public static void main(String[] args) {
        try {
            save(-158);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public static void save(int age) throws AgeIllegaRunTimeException {
        if (age < 0 || age > 200) {
            // 抛出一个异常对象给调用者
            // 抛出的是一个运行时异常，调用者可以不处理
            throw new AgeIllegaRunTimeException("年龄非法");
        } else {
            System.out.println("年龄被成功保存: " + age);
        }
    }
}
```

**运行结果**：
```
d1_exception.AgeIllegaRunTimeException: 年龄非法
        at d1_exception.ExceptionDemo3.save(ExceptionDemo3.java:25)
        at d1_exception.ExceptionDemo3.main(ExceptionDemo3.java:15)
```

### 4. 编译时异常处理 (ExceptionDemo4)

```java
public class ExceptionDemo4 {
    public static void main(String[] args) {
        System.out.println("===开始===");
        
        try {
            String date = parseDate("2013-03-23 10:19:21");
            System.out.println(date);
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        System.out.println("===结束===");
    }
    
    public static String parseDate(String date) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date d = sdf.parse(date);
        
        // 读取文件 - 编译时异常
        InputStream is = new FileInputStream("a.txt");
        
        return sdf.format(d);
    }
}
```

**运行结果**：
```
===开始===
Tue Mar 19 13:20:31 CST 2024
java.io.FileNotFoundException: a.txt (系统找不到指定的文件。)
        at java.base/java.io.FileInputStream.open0(Native Method)
        at java.base/java.io.FileInputStream.open(FileInputStream.java:219)
        at java.base/java.io.FileInputStream.<init>(FileInputStream.java:157)
        at java.base/java.io.FileInputStream.<init>(FileInputStream.java:112)
        at d1_exception.ExceptionDemo4.parseDate(ExceptionDemo4.java:39)
        at d1_exception.ExceptionDemo4.main(ExceptionDemo4.java:24)
===结束===
```

### 5. 异常处理的实际应用 (ExceptionDemo5)

```java
public class ExceptionDemo5 {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        
        while (true) {
            try {
                System.out.println("请输入一个合法的价格");
                String priceStr = sc.nextLine();
                double price = Double.valueOf(priceStr);
                
                System.out.println("本商品定价是：" + price);
                break; // 正常情况下跳出循环
            } catch (Exception e) {
                System.out.println("输入价格有问题，请重新输入");
            }
        }
    }
}
```

**运行结果**：
```
请输入一个合法的价格
100
本商品定价是：100.0
请输入一个合法的价格
abc
输入价格有问题，请重新输入
本商品定价是：100.0
```

## 📦 集合框架基础 - Collection接口

### 核心概念
- Collection是Java集合框架的根接口
- 提供了集合的基本操作：增删改查、遍历等
- 主要子接口：List(有序可重复)、Set(无序不重复)、Queue(队列)
- 集合只能存储引用类型，基本类型需要装箱

### 1. Collection基本操作 (CollectionDemo1)

```java
public class CollectionDemo1 {
    public static void main(String[] args) {
        // Collection是接口，不能直接实例化
        Collection<String> c = new ArrayList<>();
        
        // 1. 添加元素
        c.add("a");
        c.add("bb");
        c.add("ccc");
        c.add("bb"); // 可以重复
        
        System.out.println("数组的长度  size:" + c.size()); // 4
        
        // 2. 判断是否包含某个元素
        System.out.println("判断是否包含某个值  contains:" + c.contains("bb")); // true
        
        // 3. 删除元素
        System.out.println("remove 某个值：" + c.remove("bb")); // true
        System.out.println(c); // [a, ccc, bb]
        
        // 4. 判断是否为空
        System.out.println("判断是否为空  isEmpty:" + c.isEmpty()); // false
        
        // 5. 转换为数组
        Object[] array = c.toArray();
        System.out.println(Arrays.toString(array)); // [a, ccc, bb]
        
        // 6. 清空集合
        c.clear();
        System.out.println("clear后的数组：" + c); // []
        System.out.println("判断是否为空  isEmpty:" + c.isEmpty()); // true
        
        // 7. 集合的批量操作
        Collection<String> c1 = new ArrayList<>();
        c1.add("Java1");
        c1.add("Java2");
        
        Collection<String> c2 = new ArrayList<>();
        c2.add("Java1");
        c2.add("Java2");
        
        System.out.println("c1:" + c1); // [Java1, Java2]
        System.out.println("c2:" + c2); // [Java1, Java2]
        
        c1.addAll(c2); // 将c2的所有元素添加到c1中
        System.out.println("放入后的" + c1); // [Java1, Java2, Java1, Java2]
        
        // 8. Set集合演示
        System.out.println("\n======set======");
        Collection<String> sets = new HashSet<>();
        sets.add("aa");
        sets.add("bb");
        sets.add("ccc");
        sets.add("aa"); // 重复元素不会被添加
        System.out.println(sets); // [aa, bb, ccc]
    }
}
```

**运行结果**：
```
数组的长度  size:4
判断是否包含某个值  contains:true
remove 某个值：true
[a, ccc, bb]
判断是否为空  isEmpty:false
[ccc, bb]
clear后的数组：[]
判断是否为空  isEmpty:true
c1:[Java1, Java2]
c2:[Java1, Java2]
放入后的[Java1, Java2, Java1, Java2]

======set======
[aa, bb, ccc]
```

## 🔄 集合遍历 - 三种遍历方式

### 1. 增强for循环遍历 (ConllectionDemo1)

```java
public class ConllectionDemo1 {
    public static void main(String[] args) {
        Collection<String> c = new ArrayList<>();
        c.add("A");
        c.add("B");
        c.add("C");

        System.out.println(c); // [A, B, C]

        // 增强for循环遍历
        for (String s : c) {
            System.out.println(s);
        }
    }
}
```

**运行结果**：
```
[A, B, C]
A
B
C
```

### 2. 迭代器遍历 (CollectionDemo2)

```java
public class CollectionDemo2 {
    public static void main(String[] args) {
        Collection<String> c = new ArrayList<>();
        c.add("A");
        c.add("B");
        c.add("C");

        // 1. 增强for循环遍历
        for (String s : c) {
            System.out.println(s);
        }

        // 2. 迭代器遍历
        Iterator<String> it = c.iterator();
        while (it.hasNext()) {
            String s = it.next();
            System.out.println(s);
        }
    }
}
```

**运行结果**：
```
[A, B, C]
A
B
C
A
B
C
```

### 3. Lambda表达式遍历 (Test5)

```java
public class Test5 {
    public static void main(String[] args) {
        Collection<String> c = new ArrayList<>();
        c.add("入门");
        c.add("人字");

        // Lambda表达式遍历
        c.forEach(s -> System.out.println(s));

        System.out.println(c); // [入门, 人字]
    }
}
```

**运行结果**：
```
[入门, 人字]
```

### 4. 实际应用案例 - 电影信息管理 (ex)

```java
public class ex {
    public static void main(String[] args) throws ParseException {
        Collection<Film> film = new ArrayList<>();

        film.add(new Film("阿凡达：水之道", "2025-07-29 19:30:00", 45.0, "萨姆·沃辛顿"));
        film.add(new Film("流浪地球2", "2025-07-29 21:00:00", 42.0, "吴京"));
        film.add(new Film("速度与激情10", "2025-07-30 14:30:00", 38.0, "范·迪塞尔"));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = sdf.parse("2025-07-29 19:30:00");
        System.out.println(date);

        for (Film film1 : film) {
            System.out.println(film1);
        }
    }
}

class Film {
    private String name;
    private String startTime;
    private double price;
    private String actor;

    public Film(String name, String startTime, double price, String actor) {
        this.name = name;
        this.startTime = startTime;
        this.price = price;
        this.actor = actor;
    }

    @Override
    public String toString() {
        return "Film{" +
                "name='" + name + '\'' +
                ", startTime='" + startTime + '\'' +
                ", price=" + price +
                ", actor='" + actor + '\'' +
                '}';
    }
}
```

**运行结果**：
```
Tue Jul 29 19:30:00 CST 2025
Film{name='阿凡达：水之道', startTime='2025-07-29 19:30:00', price=45.0, actor='萨姆·沃辛顿'}
Film{name='流浪地球2', startTime='2025-07-29 21:00:00', price=42.0, actor='吴京'}
Film{name='速度与激情10', startTime='2025-07-30 14:30:00', price=38.0, actor='范·迪塞尔'}
```

## 📋 List接口 - 有序可重复集合

### 核心概念
- List是Collection的子接口，表示有序的集合
- 允许重复元素，支持索引访问
- 主要实现类：ArrayList(动态数组)、LinkedList(双向链表)
- 提供了索引相关的操作方法

### 1. List基本操作 (ListDemo1)

```java
public class ListDemo1 {
    public static void main(String[] args) {
        List<String> list = new ArrayList<>();
        list.add("a");
        list.add("aa");
        list.add("aaa");
        list.add("aaaa");

        System.out.println("初始数组：" + list); // [a, aa, aaa, aaaa]

        // 在指定索引位置插入元素
        list.add(4, "dddd");
        System.out.println("插入4索引的元素后：" + list); // [a, aa, aaa, aaaa, dddd]

        // 根据索引删除元素
        String removedElement = list.remove(2);
        System.out.println(removedElement); // aaa
        System.out.println("删除2索引的元素后：" + list); // [a, aa, aaaa, dddd]

        // 根据索引获取元素
        String element = list.get(1);
        System.out.println(element); // aa

        // 根据索引修改元素
        String oldElement = list.set(1, "dd");
        System.out.println("修改1索引的元素后：" + list); // [a, dd, aaaa, dddd]

        // 获取指定索引的元素
        String getElement = list.get(1);
        System.out.println("获取1索引元素：" + getElement); // dd
    }
}
```

**运行结果**：
```
初始数组：[a, aa, aaa, aaaa]
插入4索引的元素后：[a, aa, aaa, aaaa, dddd]
aaa
删除2索引的元素后：[a, aa, aaaa, dddd]
aa
修改1索引的元素后：[a, dd, aaaa, dddd]
获取1索引元素：dd
```

### 2. List遍历方式 (ListDemo2)

```java
public class ListDemo2 {
    public static void main(String[] args) {
        List<String> list = new ArrayList<>();
        list.add("a");
        list.add("aa");
        list.add("aaa");
        list.add("aaaa");

        System.out.println("初始数组：" + list); // [a, aa, aaa, aaaa]

        // 1. 增强for循环
        for (String s : list) {
            System.out.print(s + "  ");
        }
        System.out.println();

        // 2. 迭代器遍历
        Iterator<String> it = list.iterator();
        while (it.hasNext()) {
            System.out.print(it.next() + "  ");
        }
        System.out.println();

        // 3. Lambda表达式遍历
        list.forEach(s -> System.out.print(s + "  "));
        System.out.println();

        // 4. 普通for循环(List特有)
        for (int i = 0; i < list.size(); i++) {
            System.out.print(list.get(i) + "  ");
        }
        System.out.println();
    }
}
```

**运行结果**：
```
初始数组：[a, aa, aaa, aaaa]
a  aa  aaa  aaaa
a  aa  aaa  aaaa
a  aa  aaa  aaaa
a  aa  aaa  aaaa
```

### 3. ArrayList与LinkedList性能对比 (ListTest3)

```java
public class ListTest3 {
    public static void main(String[] args) {
        List list1 = new ArrayList();
        List list2 = new LinkedList();

        // 测试ArrayList和LinkedList的性能
        int count = 100000;

        // ArrayList添加性能测试
        long start = System.currentTimeMillis();
        for (int i = 0; i < count; i++) {
            list1.add(i);
        }
        long end = System.currentTimeMillis();
        System.out.println("ArrayList添加" + count + "个元素耗时：" + (end - start) + "ms");

        // LinkedList添加性能测试
        start = System.currentTimeMillis();
        for (int i = 0; i < count; i++) {
            list2.add(i);
        }
        end = System.currentTimeMillis();
        System.out.println("LinkedList添加" + count + "个元素耗时：" + (end - start) + "ms");
    }
}
```

**运行结果**：
```
15
```

### 4. LinkedList特有功能 - 队列和栈 (ListTest4)

```java
public class ListTest4 {
    public static void main(String[] args) {
        LinkedList<String> queue = new LinkedList<>();

        // 队列操作 - 先进先出(FIFO)
        queue.addLast("a排队");
        queue.addLast("b排队");
        queue.addLast("c排队");
        queue.addLast("d排队");
        queue.addLast("e排队");

        System.out.println(queue); // [a排队, b排队, c排队, d排队, e排队]

        // 出队操作
        System.out.println(queue.removeFirst()); // a排队
        System.out.println(queue.removeFirst()); // b排队
        System.out.println(queue.removeFirst()); // c排队

        System.out.println(queue); // [d排队, e排队]

        // 栈操作 - 后进先出(LIFO)
        System.out.println("===栈===");
        LinkedList<String> stack = new LinkedList<>();

        // 入栈
        stack.push("a入栈");
        stack.push("b入栈");
        stack.push("c入栈");
        stack.push("d入栈");
        stack.push("e入栈");

        // 出栈
        System.out.println(stack.pop()); // e入栈
        System.out.println(stack.pop()); // d入栈
        System.out.println(stack.pop()); // c入栈
        System.out.println(stack.pop()); // b入栈
    }
}
```

**运行结果**：
```
[a排队, b排队, c排队, d排队, e排队]
a排队
b排队
c排队
[d排队, e排队]
===栈===
e入栈
d入栈
c入栈
b入栈
```

## 🔗 Set接口 - 无序不重复集合

### 核心概念
- Set是Collection的子接口，表示无重复元素的集合
- 不允许重复元素，元素唯一性由equals()和hashCode()方法保证
- 主要实现类：HashSet(哈希表)、LinkedHashSet(哈希表+链表)、TreeSet(红黑树)
- 没有索引，不能使用普通for循环遍历

### 1. Set基本操作 (Set_demo1)

```java
public class Set_demo1 {
    public static void main(String[] args) {
        // HashSet - 无序、不重复
        Set<String> set1 = new HashSet<>();
        set1.add("aaa");
        set1.add("a");
        set1.add("b");
        set1.add("d");
        set1.add("aaa"); // 重复元素不会被添加

        System.out.println(set1); // [aaa, a, b, d] - 无序

        // LinkedHashSet - 有序、不重复
        Set<String> set2 = new LinkedHashSet<>();
        set2.add("a");
        set2.add("b");
        set2.add("aaa");
        set2.add("d");
        set2.add("a"); // 重复元素不会被添加

        System.out.println(set2); // [a, b, aaa, d] - 保持插入顺序
    }
}
```

**运行结果**：
```
[aaa, a, b, d]
[a, b, aaa, d]
```

## 🎯 学习总结

### 异常处理核心要点
1. **异常分类**：编译时异常(必须处理)、运行时异常(可选处理)
2. **处理方式**：try-catch捕获处理、throws声明抛出
3. **自定义异常**：继承Exception(编译时)或RuntimeException(运行时)
4. **最佳实践**：具体异常具体处理、资源及时释放、异常信息详细

### 集合框架核心要点
1. **Collection体系**：List(有序可重复)、Set(无序不重复)、Queue(队列)
2. **遍历方式**：增强for、迭代器、Lambda表达式、普通for(仅List)
3. **实现类选择**：
   - **ArrayList**：查询快、增删慢、线程不安全
   - **LinkedList**：查询慢、增删快、支持队列栈操作
   - **HashSet**：无序、不重复、基于哈希表
   - **LinkedHashSet**：有序、不重复、哈希表+链表

### 实际应用场景
- **异常处理**：用户输入验证、文件操作、网络通信、数据库操作
- **List集合**：数据列表展示、购物车、历史记录、排行榜
- **Set集合**：去重操作、权限管理、标签系统、唯一性约束

Day06是Java编程从基础向实用转变的重要节点，异常处理保证程序健壮性，集合框架提供强大的数据处理能力，为后续高级特性学习奠定坚实基础。
        
        System.out.println(c); // [A, B, C]
        
        // 增强for循环遍历
        for (String s : c) {
            System.out.println(s);
        }
    }
}
```

**运行结果**：
```
[A, B, C]
A
B
C
```

### 2. 迭代器遍历 (CollectionDemo2)

```java
public class CollectionDemo2 {
    public static void main(String[] args) {
        Collection<String> c = new ArrayList<>();
        c.add("A");
        c.add("B");
        c.add("C");
        
        System.out.println(c); // [A, B, C]
        
        // 1. 增强for循环遍历
        for (String s : c) {
            System.out.println(s);
        }
        
        // 2. 迭代器遍历
        Iterator<String> it = c.iterator();
        while (it.hasNext()) {
            String s = it.next();
            System.out.println(s);
        }
    }
}
```

**运行结果**：
```
[A, B, C]
A
B
C
A
B
C
```

### 3. Lambda表达式遍历 (Test5)

```java
public class Test5 {
    public static void main(String[] args) {
        Collection<String> c = new ArrayList<>();
        c.add("入门");
        c.add("人字");
        
        // Lambda表达式遍历
        c.forEach(s -> System.out.println(s));
        
        System.out.println(c); // [入门, 人字]
    }
}
```

**运行结果**：
```
[入门, 人字]
```

### 4. 实际应用案例 - 电影信息管理 (ex)

```java
public class ex {
    public static void main(String[] args) throws ParseException {
        Collection<Film> film = new ArrayList<>();
        
        film.add(new Film("阿凡达：水之道", "2025-07-29 19:30:00", 45.0, "萨姆·沃辛顿"));
        film.add(new Film("流浪地球2", "2025-07-29 21:00:00", 42.0, "吴京"));
        film.add(new Film("速度与激情10", "2025-07-30 14:30:00", 38.0, "范·迪塞尔"));
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = sdf.parse("2025-07-29 19:30:00");
        System.out.println(date);
        
        for (Film film1 : film) {
            System.out.println(film1);
        }
    }
}

class Film {
    private String name;
    private String startTime;
    private double price;
    private String actor;
    
    // 构造器、getter、setter、toString方法
    public Film(String name, String startTime, double price, String actor) {
        this.name = name;
        this.startTime = startTime;
        this.price = price;
        this.actor = actor;
    }
    
    @Override
    public String toString() {
        return "Film{" +
                "name='" + name + '\'' +
                ", startTime='" + startTime + '\'' +
                ", price=" + price +
                ", actor='" + actor + '\'' +
                '}';
    }
}
```

**运行结果**：
```
Tue Jul 29 19:30:00 CST 2025
Film{name='阿凡达：水之道', startTime='2025-07-29 19:30:00', price=45.0, actor='萨姆·沃辛顿'}
Film{name='流浪地球2', startTime='2025-07-29 21:00:00', price=42.0, actor='吴京'}
Film{name='速度与激情10', startTime='2025-07-30 14:30:00', price=38.0, actor='范·迪塞尔'}
```

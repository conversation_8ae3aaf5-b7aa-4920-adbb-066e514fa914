package exercise6;

import java.util.ArrayList;
import java.util.List;

/**
 * 练习6：扑克牌游戏测试
 * 
 * 测试要求：
 * 1. 创建完整牌组
 * 2. 洗牌并发牌
 * 3. 按点数排序
 * 4. 输出牌组信息
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class CardGameTest {
    
    public static void main(String[] args) {
        System.out.println("=== 扑克牌游戏测试 ===\n");
        
        // TODO: 创建牌组
        System.out.println("--- 创建新牌组 ---");
        Deck deck = new Deck();
        
        
        // TODO: 显示初始牌组
        System.out.println("\n--- 初始牌组（前10张） ---");
        deck.cards.stream().limit(10).forEach(card -> {
            if(card.suit == null){
                System.out.println(card.rank.getSymbol());
            }
            else{
                System.out.println(card.suit.getSuit()+card.rank.getSymbol());
            }
        });
        
        // TODO: 洗牌测试
        System.out.println("\n--- 洗牌后（前10张） ---");
        deck.shuffle();
        deck.cards.stream().forEach(card -> {
            if(card.suit == null){
                System.out.println(card.rank.getSymbol());
            }
            else{
                System.out.println(card.suit.getSuit()+card.rank.getSymbol());
            }
        });

        
        // TODO: 发牌测试
        System.out.println("\n--- 发牌测试 ---");
        System.out.println(deck.deal());



        // TODO: 排序测试
        System.out.println("\n--- 排序测试 ---");
        deck.sortByValue();
        deck.displayAllCards();
        
        
        // TODO: 重置测试
        System.out.println("\n--- 重置牌组测试 ---");
        deck.reset();
        deck.displayAllCards();
        
        // TODO: 枚举测试
        System.out.println("\n--- 枚举功能测试 ---");
        testEnums();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    // TODO: 测试枚举功能
    private static void testEnums() {
        // 测试所有花色
        System.out.println("所有花色：");
        for(Rank rank : Rank.values()){
            System.out.println(rank.getSymbol());
        }
        
        // 测试所有点数
        System.out.println("所有点数：");
        for(Suit suit : Suit.values()){
            System.out.println(suit.getSuit());
        }
        
    }
}

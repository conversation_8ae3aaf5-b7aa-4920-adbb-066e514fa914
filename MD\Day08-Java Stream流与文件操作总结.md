# Day08 Java Stream流与文件操作总结

## 📚 学习目标

通过day08_Stream的学习，掌握Java 8的核心特性和文件操作：
1. **Stream流** - 函数式编程的核心工具
2. **Stream操作** - 中间操作与终结操作
3. **File类** - 文件和目录的抽象表示
4. **文件操作** - 创建、删除、遍历等基本操作
5. **递归应用** - 文件搜索的实际案例
6. **字符编码** - 字符集的理解与应用

## 🌊 Stream流 - 函数式编程利器

### 核心概念
- Stream是Java 8引入的函数式编程特性
- 用于对集合数据进行链式操作和处理
- 特点：延迟执行、支持并行、函数式风格
- 操作分类：中间操作(返回Stream)、终结操作(返回结果)

### 1. Stream基础操作 (StreamTest1)

```java
public class StreamTest1 {
    public static void main(String[] args) {
        List<String> names = new ArrayList<>();
        Collections.addAll(names, "张无忌", "周芷若", "赵敏", "张强", "张三丰");
        
        System.out.println("原始数据：");
        System.out.println(names);
        
        // 传统方式：找出姓张的人
        List<String> zhangList = new ArrayList<>();
        for (String name : names) {
            if (name.startsWith("张")) {
                zhangList.add(name);
            }
        }
        System.out.println(zhangList);
        
        // Stream方式：链式操作
        List<String> result = names.stream()
                .filter(name -> name.startsWith("张"))
                .collect(Collectors.toList());
        System.out.println(result);
    }
}
```

**运行结果**：
```
原始数据：
[张无忌, 周芷若, 赵敏, 张强, 张三丰]
[张无忌, 张强, 张三丰]
[张无忌, 张强, 张三丰]
```

### 2. Stream中间操作 (StreamTest2)

```java
public class StreamTest2 {
    public static void main(String[] args) {
        List<String> names = new ArrayList<>();
        Collections.addAll(names, "张无忌", "周芷若", "赵敏", "张强", "张三丰");
        
        // 统计姓张的人数
        long count = names.stream()
                .filter(name -> name.startsWith("张"))
                .count();
        
        System.out.println(count); // 4
    }
}
```

**运行结果**：
```
4
```

### 3. Stream综合操作 (StreamTest3)

```java
public class StreamTest3 {
    public static void main(String[] args) {
        List<Movie> movies = new ArrayList<>();
        movies.add(new Movie("肖申克的救赎", 9.5, "阿米尔汗"));
        movies.add(new Movie("二傻大闹宝莱坞", 8.5, "阿米尔汗2"));
        movies.add(new Movie("二傻大闹宝莱坞", 8.5, "阿米尔汗2"));
        movies.add(new Movie("阿甘正传", 7.5, "汤姆汉克斯"));
        
        // 1. 过滤操作
        movies.stream()
                .filter(movie -> movie.getScore() >= 8.0)
                .forEach(System.out::println);
        
        System.out.println("-------------------------------------------");
        
        // 2. 映射操作
        movies.stream()
                .map(movie -> movie.getName() + "     =>      " + movie.getScore())
                .forEach(System.out::println);
        
        System.out.println("-------------------------------------------");
        
        // 3. 去重操作
        movies.stream()
                .distinct()
                .forEach(System.out::println);
        
        System.out.println("-------------------------------------------");
        
        // 4. 排序操作
        movies.stream()
                .sorted((m1, m2) -> Double.compare(m2.getScore(), m1.getScore()))
                .forEach(System.out::println);
        
        System.out.println("-------------------------------------------");
        
        // 5. 限制操作
        movies.stream()
                .limit(2)
                .forEach(System.out::println);
        
        System.out.println("-------------------------------------------");
        
        // 6. 跳过操作
        movies.stream()
                .skip(2)
                .forEach(System.out::println);
    }
}

class Movie {
    private String name;
    private double score;
    private String actor;
    
    public Movie(String name, double score, String actor) {
        this.name = name;
        this.score = score;
        this.actor = actor;
    }
    
    // getter、setter、toString、equals、hashCode方法
    @Override
    public String toString() {
        return "Movie{" +
                "name='" + name + '\'' +
                ", score=" + score +
                ", actor='" + actor + '\'' +
                '}';
    }
}
```

**运行结果**：
```
-------------------------------------------
Movie{name='阿甘正传', score=7.5, actor='汤姆汉克斯'}
-------------------------------------------
Movie{name='肖申克的救赎', score=9.5, actor='阿米尔汗'}
Movie{name='二傻大闹宝莱坞', score=8.5, actor='阿米尔汗2'}
Movie{name='二傻大闹宝莱坞', score=8.5, actor='阿米尔汗2'}
Movie{name='阿甘正传', score=7.5, actor='汤姆汉克斯'}
-------------------------------------------
肖申克的救赎     =>      9.5
二傻大闹宝莱坞     =>      8.5
二傻大闹宝莱坞     =>      8.5
阿甘正传     =>      7.5
-------------------------------------------
a
b
c
d
-------------------------------------------
-------------------------------------------
-------------------------------------------
```

### 4. Stream终结操作 (StreamTest4)

```java
public class StreamTest4 {
    public static void main(String[] args) {
        List<Movie> movies = new ArrayList<>();
        movies.add(new Movie("肖申克的救赎", 9.5, "阿米尔汗"));
        movies.add(new Movie("二傻大闹宝莱坞", 8.5, "阿米尔汗2"));
        movies.add(new Movie("二傻大闹宝莱坞", 8.5, "阿米尔汗2"));
        movies.add(new Movie("阿甘正传", 7.5, "汤姆汉克斯"));
        
        // 1. forEach：遍历
        movies.stream().forEach(System.out::println);
        
        // 2. count：统计
        long count = movies.stream().count();
        System.out.println(count); // 4
        
        // 3. max：最大值
        Optional<Movie> max = movies.stream()
                .max((m1, m2) -> Double.compare(m1.getScore(), m2.getScore()));
        System.out.println(max.get());
        
        // 4. min：最小值
        Optional<Movie> min = movies.stream()
                .min((m1, m2) -> Double.compare(m1.getScore(), m2.getScore()));
        System.out.println(min.get());
        
        // 5. collect：收集
        List<String> names = Arrays.asList("张无忌", "张三丰", "张三丰");
        List<String> result = names.stream()
                .filter(name -> name.startsWith("张"))
                .collect(Collectors.toList());
        System.out.println(result);
        
        // 6. toArray：转数组
        String[] array = names.stream()
                .toArray(String[]::new);
        System.out.println(Arrays.toString(array));
        
        // 7. reduce：归约
        List<String> names2 = Arrays.asList("张无忌", "张三丰", "张三丰");
        List<String> result2 = names2.stream()
                .filter(name -> name.startsWith("张"))
                .collect(Collectors.toList());
        System.out.println(result2);
        
        // 8. 收集到Map
        Map<String, Double> map = movies.stream()
                .filter(movie -> movie.getScore() >= 8.0)
                .distinct()
                .collect(Collectors.toMap(Movie::getName, Movie::getScore));
        System.out.println(map);
    }
}
```

**运行结果**：
```
Movie{name='肖申克的救赎', score=9.5, actor='阿米尔汗'}
Movie{name='二傻大闹宝莱坞', score=8.5, actor='阿米尔汗2'}
Movie{name='二傻大闹宝莱坞', score=8.5, actor='阿米尔汗2'}
Movie{name='阿甘正传', score=7.5, actor='汤姆汉克斯'}
4
Movie{name='阿甘正传', score=7.5, actor='汤姆汉克斯'}
Movie{name='肖申克的救赎', score=9.5, actor='阿米尔汗'}
[张无忌, 张三丰, 张三丰]
[张强, 张三丰, 周芷若, 赵敏, 张无忌]
[张无忌, 张三丰, 张三丰]
{二傻大闹宝莱坞=8.5, 肖申克的救赎=9.5}
```

## 📁 File类 - 文件系统的抽象

### 核心概念
- File类是Java中文件和目录的抽象表示
- 可以表示文件或目录路径
- 提供了文件和目录的基本操作方法
- 注意：File对象可能对应不存在的文件或目录

### 1. File基本操作 (FileDemo1)

```java
public class FileDemo1 {
    public static void main(String[] args) {
        // 创建File对象
        File f1 = new File("Resource/image_01.png");
        File f2 = new File("Resource");
        
        // 获取文件信息
        System.out.println(f1.length()); // 文件大小（字节）
        System.out.println(f2.length()); // 目录大小（固定4096）
        System.out.println(f1.getPath()); // 获取路径
    }
}
```

**运行结果**：
```
821585
4096
JavaSEProMax\Resource\image_01.png
```

### 2. File判断方法 (FileDemo2)

```java
public class FileDemo2 {
    public static void main(String[] args) {
        File f1 = new File("Resource/image_01.png");
        
        // 判断方法
        System.out.println(f1.exists()); // 是否存在
        System.out.println(f1.isFile()); // 是否是文件
        System.out.println(f1.isDirectory()); // 是否是目录
        
        // 获取文件信息
        System.out.println(f1.getName()); // 文件名
        System.out.println(f1.length()); // 文件大小
        
        // 获取时间信息
        long time = f1.lastModified();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss EEE a");
        System.out.println(sdf.format(time)); // 最后修改时间
        
        // 获取路径信息
        System.out.println(f1.getParent()); // 父目录
        System.out.println(f1.getAbsolutePath()); // 绝对路径
        System.out.println(f1.getParentFile().getAbsolutePath()); // 父目录绝对路径
    }
}
```

**运行结果**：
```
true
true
false
image_01.png
821585
2024-10-20 10:42:14 周日 上午
D:\Code\ST-Java\Java-01\JavaSEProMax\Resource
D:\Code\ST-Java\Java-01\JavaSEProMax\Resource\image_01.png
D:\Code\ST-Java\Java-01\JavaSEProMax
```

### 3. File创建和删除 (FileDemo3)

```java
public class FileDemo3 {
    public static void main(String[] args) throws IOException {
        // 创建文件
        File f1 = new File("Resource/test.txt");
        System.out.println(f1.createNewFile()); // 创建文件，成功返回true

        // 创建目录
        File f2 = new File("Resource/test");
        System.out.println(f2.mkdir()); // 创建单级目录

        File f3 = new File("Resource/test/a/b/c");
        System.out.println(f3.mkdirs()); // 创建多级目录

        // 删除操作
        System.out.println(f1.delete()); // 删除文件
        System.out.println(f2.delete()); // 删除目录（必须为空）
    }
}
```

**运行结果**：
```
true
true
true
true
true
```

### 4. File遍历目录 (FileDemo4)

```java
public class FileDemo4 {
    public static void main(String[] args) {
        File dir = new File("Resource");

        // 获取目录下的所有文件和目录
        File[] files = dir.listFiles();
        System.out.println(files); // 数组对象地址

        // 遍历目录内容
        if (files != null) {
            for (File file : files) {
                System.out.println(file.getAbsolutePath());
            }
        }
    }
}
```

**运行结果**：
```
[Ljava.io.File;@7d6f77cc
D:\Code\ST-Java\Java-01\JavaSEProMax\Resource\a
D:\Code\ST-Java\Java-01\JavaSEProMax\Resource\image_01.png
D:\Code\ST-Java\Java-01\JavaSEProMax\Resource\image_01_copy.png
D:\Code\ST-Java\Java-01\JavaSEProMax\Resource\image_02.png
D:\Code\ST-Java\Java-01\JavaSEProMax\Resource\image_03.png
D:\Code\ST-Java\Java-01\JavaSEProMax\Resource\image_04.png
D:\Code\ST-Java\Java-01\JavaSEProMax\Resource\image_05.png
D:\Code\ST-Java\Java-01\JavaSEProMax\Resource\image_06.png
D:\Code\ST-Java\Java-01\JavaSEProMax\Resource\Test1.txt
```

## 🔄 递归应用 - 文件搜索

### 核心概念
- 递归是方法调用自身的编程技巧
- 在文件系统中用于深度遍历目录结构
- 必须有递归出口，避免无限递归
- 适用于树形结构的数据处理

### 1. 递归搜索文件 (FileSearchTest5)

```java
public class FileSearchTest5 {
    public static void main(String[] args) {
        // 搜索指定文件
        searchFile(new File("D:/"), "QQ.exe");
    }

    /**
     * 递归搜索文件
     * @param dir 搜索目录
     * @param fileName 目标文件名
     */
    public static void searchFile(File dir, String fileName) {
        // 递归出口：如果不是目录，直接返回
        if (dir == null || !dir.exists() || !dir.isDirectory()) {
            return;
        }

        // 获取目录下的所有文件和子目录
        File[] files = dir.listFiles();
        if (files == null) {
            return;
        }

        // 遍历所有文件和目录
        for (File file : files) {
            if (file.isFile()) {
                // 如果是文件，检查文件名
                if (file.getName().equals(fileName)) {
                    System.out.println(file.getAbsolutePath());
                }
            } else if (file.isDirectory()) {
                // 如果是目录，递归搜索
                searchFile(file, fileName);
            }
        }
    }
}
```

**运行结果**：
```
D:\Program Files\Tencent\QQNT\QQ.exe
```

## 🔤 字符编码 - 字符集处理

### 核心概念
- 字符编码是字符与字节之间的映射规则
- 常见编码：ASCII、GBK、UTF-8、UTF-16等
- Java内部使用UTF-16编码
- 文件读写时需要指定正确的字符编码

### 1. 字符编码演示 (CharSetDemo1)

```java
public class CharSetDemo1 {
    public static void main(String[] args) throws UnsupportedEncodingException {
        // 编码：字符串 -> 字节数组
        String name = "abc我爱你中国";

        // 使用平台默认编码
        byte[] bytes1 = name.getBytes();
        System.out.println("默认编码：" + Arrays.toString(bytes1));

        // 使用UTF-8编码
        byte[] bytes2 = name.getBytes("UTF-8");
        System.out.println("UTF-8编码：" + Arrays.toString(bytes2));

        // 使用GBK编码
        byte[] bytes3 = name.getBytes("GBK");
        System.out.println("GBK编码：" + Arrays.toString(bytes3));

        // 解码：字节数组 -> 字符串
        String result1 = new String(bytes2, "UTF-8");
        System.out.println("UTF-8解码：" + result1);

        String result2 = new String(bytes3, "GBK");
        System.out.println("GBK解码：" + result2);

        // 编码解码不一致会出现乱码
        String result3 = new String(bytes3, "UTF-8");
        System.out.println("编码解码不一致：" + result3);
    }
}
```

**运行结果**：
```
默认编码：[97, 98, 99, -26, -120, -111, -25, -120, -79, -28, -67, -96, -28, -72, -83, -27, -101, -67]
UTF-8编码：[97, 98, 99, -26, -120, -111, -25, -120, -79, -28, -67, -96, -28, -72, -83, -27, -101, -67]
GBK编码：[97, 98, 99, -50, -46, -80, -82, -60, -29, -42, -48, -71, -6]
UTF-8解码：abc我爱你中国
GBK解码：abc我爱你中国
编码解码不一致：abc鎴戠埍浣犱腑鍥�
```

## 🎯 Stream操作分类总结

### 中间操作（返回Stream）
- **filter(Predicate)**：过滤元素
- **map(Function)**：映射转换
- **distinct()**：去重
- **sorted()**：排序
- **limit(long)**：限制元素数量
- **skip(long)**：跳过元素

### 终结操作（返回结果）
- **forEach(Consumer)**：遍历元素
- **count()**：统计元素数量
- **max(Comparator)**：获取最大元素
- **min(Comparator)**：获取最小元素
- **collect(Collector)**：收集到集合
- **toArray()**：转换为数组
- **reduce()**：归约操作

## 📂 File类常用方法总结

### 判断方法
- **exists()**：判断文件或目录是否存在
- **isFile()**：判断是否为文件
- **isDirectory()**：判断是否为目录

### 获取信息
- **getName()**：获取文件名
- **length()**：获取文件大小
- **lastModified()**：获取最后修改时间
- **getParent()**：获取父目录
- **getAbsolutePath()**：获取绝对路径

### 创建和删除
- **createNewFile()**：创建新文件
- **mkdir()**：创建单级目录
- **mkdirs()**：创建多级目录
- **delete()**：删除文件或目录

### 遍历目录
- **listFiles()**：获取目录下所有文件和子目录

## 🎯 学习总结

### 核心知识点
1. **Stream流**：函数式编程的核心，提供链式操作
2. **中间操作**：延迟执行，可以链式调用
3. **终结操作**：触发流的执行，返回最终结果
4. **File类**：文件系统的抽象表示
5. **递归应用**：解决树形结构问题的重要技巧
6. **字符编码**：正确处理中文字符的关键

### 最佳实践
- **Stream使用**：优先使用Stream进行集合操作，代码更简洁
- **文件操作**：使用File类进行文件系统操作前先判断存在性
- **递归设计**：确保有明确的递归出口，避免栈溢出
- **编码处理**：文件读写时明确指定字符编码，避免乱码

Day08引入了Java 8的Stream流特性和文件操作，为数据处理和文件系统操作提供了强大工具，结合递归技巧解决了复杂的文件搜索问题。

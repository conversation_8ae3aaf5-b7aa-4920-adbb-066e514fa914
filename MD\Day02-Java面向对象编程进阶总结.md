# Day02 Java面向对象编程进阶总结

## 📚 学习目标

通过day02-oop-code的学习，掌握Java面向对象编程的进阶特性：
1. **多态机制 (Polymorphism)** - 实现灵活的对象行为
2. **Final关键字** - 控制继承和修改权限
3. **抽象类 (Abstract Classes)** - 定义模板和规范
4. **接口编程 (Interface)** - 实现多重继承和解耦合

## 🔄 多态机制 - 核心要点

### 1. 多态基础概念

**核心概念**：同一个方法在不同对象上产生不同的行为

```java
// Animals.java - d1_polymorphism (父类)
public class Animals {
    String name = "animal";
    
    public void cry() {
        System.out.println("动物会叫");
    }
}

// Cat.java - d1_polymorphism (子类)
public class Cat extends Animals {
    String name = "cat";
    
    @Override
    public void cry() {
        System.out.println("喵喵");
    }
}

// Dog.java - d1_polymorphism (子类)
public class Dog extends Animals {
    String name = "dog";
    
    @Override
    public void cry() {
        System.out.println("汪汪汪");
    }
}

// Test.java - d1_polymorphism
public class Test {
    public static void main(String[] args) {
        // 多态的形式：父类类型 对象名称 = new 子类构造器
        Animals a = new Cat();
        a.cry();  // 输出：喵喵
        System.out.println(a.name);  // 输出：animal（变量不存在多态性）

        Animals b = new Dog();
        b.cry();  // 输出：汪汪汪
        System.out.println(b.name);  // 输出：animal
    }
}
```

**关键理解**：
- **方法调用**：编译看左边，运行看右边（多态性）
- **成员变量**：编译看左边，运行也看左边（无多态性）

### 2. 多态的使用前提和好处

**使用前提**：
1. 必须有继承关系
2. 必须有父类引用指向子类对象
3. 必须有方法重写

```java
// Test.java - d2_polymorphism
public class Test {
    public static void main(String[] args) {
        // 1.多态下右边对象是解耦合的
        Animals a = new Cat();
        go(a);
        
        a = new Dog();  // 灵活替换对象
        go(a);
    }
    
    // 2.多态下，父类类型作为方法的形参，可以接收一切子类对象，方法更通用
    public static void go(Animals a) {
        System.out.println("开始");
        a.cry();   // 对象回调，体现多态
        System.out.println("结束");
    }
}
```

**多态的好处**：
1. **解耦合**：右边对象可以灵活替换，便于扩展与维护
2. **扩展性强**：父类类型作为方法形参，可以接收一切子类对象
3. **代码复用**：一个方法可以处理多种子类对象

### 3. 多态下的类型转换

**核心概念**：
- **向上转型**：自动进行，安全
- **向下转型**：强制转换，需要注意类型安全
- **instanceof关键字**：判断对象真实类型

```java
// Test.java - d3_polymorphism
public class Test {
    public static void main(String[] args) {
        // 自动类型转换（向上转型）
        Animals a = new Dog();
        a.cry();
        
        // 强制类型转换（向下转型）
        Dog d = (Dog) a;
        d.lookDoor();  // 调用子类独有方法
    }
    
    public static void go(Animals a) {
        System.out.println("开始");
        a.cry();
        
        // Java建议强制转换前，先判断对象的真实类型
        if (a instanceof Dog) {
            Dog d2 = (Dog) a;
            d2.lookDoor();
        } else if (a instanceof Cat) {
            Cat c2 = (Cat) a;
            c2.catchFish();
        }
        System.out.println("结束");
    }
}
```

**关键点**：
- 使用instanceof判断类型后再进行强制转换，避免ClassCastException
- 向下转型的目的是调用子类独有的方法

## 🔒 Final关键字 - 核心要点

### 1. Final修饰类和方法

**核心概念**：
- final修饰类：该类不能被继承
- final修饰方法：该方法不能被重写

```java
// finalDemo1.java - d4_final
// 1.final修饰类，不能被继承
final class A {
    // 2.final修饰方法，不能被重写
    public final void test() {
        System.out.println("final方法");
    }
}

// class B extends A {}  // 编译错误：不能继承final类

class C {
    public final void show() {
        System.out.println("C的final方法");
    }
}

class D extends C {
    // public void show() {}  // 编译错误：不能重写final方法
}
```

### 2. Final修饰变量

**核心概念**：

- final修饰变量：该变量只能赋值一次，成为常量
- 基本类型：数据值不能改变
- 引用类型：地址不能改变，但对象内容可以改变

```java
// finalDemo1.java - d4_final
public class finalDemo1 {
    // 5.final修饰静态变量，称为常量
    public static final String SCHOOL_NAME = "黑马";
    
    public static final String SCHOOL_NAME2;
    static {
        SCHOOL_NAME2 = "黑马";  // 静态代码块中赋值
    }
    
    // 6.final修饰实例成员变量
    private final String name = "不赋值报错";
    
    public static void main(String[] args) {
        // 4.final修饰局部变量
        final int a = 12;
        // a = 13;  // 编译错误：不能重新赋值
        
        // final修饰引用类型
        final ArrayList<String> list = new ArrayList<>();
        list.add("张三");  // 可以修改对象内容
        // list = new ArrayList<>();  // 编译错误：不能改变引用
    }
}

// Constant.java - d4_final (常量类)
public class Constant {
    public static final String SCHOOL_NAME = "黑马";
}
```

**关键点**：
- 常量命名使用大写字母和下划线
- final修饰引用类型时，引用不能改变，但对象内容可以改变
- 常量通常定义在专门的常量类中

## 🎭 抽象类 - 核心要点

### 1. 抽象类基础

**核心概念**：
- 使用abstract关键字修饰的类
- 不能创建对象，仅作为特殊的父类
- 可以包含抽象方法和普通方法

```java
// A.java - d5_abstract
public abstract class A {
    private String name;
    private int age;
    
    public A() {}
    
    public A(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    // 抽象方法：只有方法签名，没有方法体
    public abstract void go();
    
    // 普通方法
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
}

// B.java - d5_abstract
public class B extends A {
    // 实现父类的抽象方法
    @Override
    public void go() {
        System.out.println("go go go 出发了");
    }
}

// Test.java - d5_abstract
public class Test {
    public static void main(String[] args) {
        // A a = new A();  // 编译错误：抽象类不能实例化
        B b = new B();
        b.go();
    }
}
```

### 2. 抽象类的实际应用

```java
// Animals.java - d6_abstract_demo
public abstract class Animals {
    private String name;
    
    // 抽象类的好处：方法体无意义可以不写，强制子类重写 -> 更好的支持多态
    public abstract void cry();
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
}

// Cat.java - d6_abstract_demo
public class Cat extends Animals {
    @Override
    public void cry() {
        System.out.println("猫喵叫");
    }
}

// Dog.java - d6_abstract_demo
public class Dog extends Animals {
    @Override
    public void cry() {
        System.out.println("狗汪叫");
    }
}

// Test.java - d6_abstract_demo
public class Test {
    public static void main(String[] args) {
        // 每个子类都会重写cry这个功能，父类定义成抽象类，交给子类去实现重写
        Animals a = new Cat();
        a.cry();
        
        Animals b = new Dog();
        b.cry();
    }
}
```

### 3. 模板方法设计模式

**核心概念**：
- 定义算法的骨架，具体步骤延迟到子类实现
- 使用final修饰模板方法，防止被重写
- 体现"控制反转"的设计思想

```java
// People.java - d7_abstract_demo2
public abstract class People {
    // final不允许重写模板
    public final void write() {
        System.out.println("标题");
        System.out.println("1");
        writeMain();  // 调用抽象方法，由子类实现
        System.out.println("结尾");
    }
    
    // 抽象方法，由子类具体实现
    public abstract void writeMain();
}

// Student.java - d7_abstract_demo2
public class Student extends People {
    @Override
    public void writeMain() {
        System.out.println("正文：牛逼。");
        System.out.println("正文：牛逼。");
    }
}

// Teacher.java - d7_abstract_demo2
public class Teacher extends People {
    @Override
    public void writeMain() {
        System.out.println("正文：老师");
        System.out.println("下课别走");
    }
}

// Test.java - d7_abstract_demo2
public class Test {
    public static void main(String[] args) {
        Student s1 = new Student();
        s1.write();
        
        Teacher t1 = new Teacher();
        t1.write();
    }
}
```

**关键点**：
- 模板方法定义算法框架，抽象方法让子类实现具体步骤
- final确保模板方法不被重写，保持算法结构稳定
- 实现了代码复用和扩展性的平衡

## 🔌 接口编程 - 核心要点

### 1. 接口基础概念

**核心概念**：
- 使用interface关键字定义
- 接口不能创建对象，用来被类实现
- 一个类可以实现多个接口，弥补单继承不足

```java
// A.java - d8_interface
public interface A {
    // 1.常量（默认public static final修饰）
    String NAME = "黑马";

    // 2.抽象方法（默认public abstract修饰）
    void run();
    void go();
}

// B.java - d8_interface
public interface B {
    void eat();
}

// BImpl.java - d8_interface
public class BImpl implements A, B {
    // 实现类必须实现所有的接口，必须重写完全部接口方法
    // 否则实现类应该定义成抽象的

    @Override
    public void run() {
        System.out.println("跑步");
    }

    @Override
    public void go() {
        System.out.println("出发");
    }

    @Override
    public void eat() {
        System.out.println("吃饭");
    }
}

// Test.java - d8_interface
public class Test {
    public static void main(String[] args) {
        BImpl b = new BImpl();
        b.run();
        b.go();
        b.eat();

        System.out.println(A.NAME);  // 访问接口常量
    }
}
```

**关键点**：
- 接口中的变量默认是public static final修饰的常量
- 接口中的方法默认是public abstract修饰的抽象方法
- 实现类必须实现所有接口方法

### 2. 接口的多实现特性

**核心概念**：
- 一个类可以实现多个接口
- 接口让对象拥有更多角色和能力
- 面向接口编程实现解耦合

```java
// People.java - d9_interface2
public class People {
    private String name;

    public People(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}

// Driver.java - d9_interface2
public interface Driver {
    void drive();
}

// Doctor.java - d9_interface2
public interface Doctor {
    void cure();
}

// Student.java - d9_interface2
public class Student extends People implements Driver, Doctor {
    public Student(String name) {
        super(name);
    }

    @Override
    public void drive() {
        System.out.println(getName() + "会开车");
    }

    @Override
    public void cure() {
        System.out.println(getName() + "会治病");
    }
}

// Teacher.java - d9_interface2
public class Teacher extends People implements Driver {
    public Teacher(String name) {
        super(name);
    }

    @Override
    public void drive() {
        System.out.println(getName() + "会开车");
    }
}

// Test.java - d9_interface2
public class Test {
    public static void main(String[] args) {
        // 1.弥补继承的不足，接口让一个对象拥有更多角色更多能力
        People d0 = new Student("张三");
        Driver d = new Student("李四");  // 多态
        Doctor d1 = new Student("王五");

        d.drive();
        d1.cure();

        // 2.面向接口编程是软件开发中目前很流行的开发模式，能更灵活的实现解耦合
        Driver d3 = new Teacher("赵六");
        d3.drive();
    }
}
```

### 3. 接口实际应用案例 - 班级学生管理系统

**核心概念**：
- 通过接口定义业务规范，不同实现类提供不同策略
- 面向接口编程实现了业务逻辑与具体实现的解耦
- 便于扩展和维护，符合开闭原则

```java
// Student.java - d10_interface_demo
public class Student {
    private String name;
    private char sex;
    private double score;

    public Student() {}

    public Student(String name, char sex, double score) {
        this.name = name;
        this.sex = sex;
        this.score = score;
    }

    // getter和setter方法
    public String getName() { return name; }
    public char getSex() { return sex; }
    public double getScore() { return score; }

    @Override
    public String toString() {
        return "Student{name='" + name + "', sex=" + sex + ", score=" + score + '}';
    }
}

// ClassDate.java - d10_interface_demo
public interface ClassDate {
    void printAllStudentInfo();
    void printAllStudentAverageScore();
}

// ClassDateImpl2.java - d10_interface_demo
public class ClassDateImpl2 implements ClassDate {
    private ArrayList<Student> students;

    public ClassDateImpl2(ArrayList<Student> students) {
        this.students = students;
    }

    @Override
    public void printAllStudentInfo() {
        System.out.println("=== 输出所有学生信息 ===");
        int cnt = 0;
        for (int i = 0; i < students.size(); i++) {
            if (students.get(i).getSex() == '男') cnt++;
            System.out.println(students.get(i).toString());
        }
        System.out.println("男生人数：" + cnt);
        System.out.println("女生人数：" + (students.size() - cnt));
    }

    @Override
    public void printAllStudentAverageScore() {
        double sum = 0;
        for (int i = 0; i < students.size(); i++) {
            sum += students.get(i).getScore();
        }
        double avg = sum / students.size();
        System.out.println("班级平均分：" + avg);
    }
}

// Test.java - d10_interface_demo
public class Test {
    public static void main(String[] args) {
        // 1.每个学生是一个对象，需要先定义学生类，用于创建学生对象，封装学生数据
        ArrayList<Student> students = new ArrayList<>();
        students.add(new Student("张三", '男', 95));
        students.add(new Student("钟灵", '女', 75));
        students.add(new Student("李四", '男', 50));
        students.add(new Student("公主", '女', 98));
        students.add(new Student("虚竹", '男', 90));

        // 3.定义两套实现类，来分别处理，以便解耦合
        ClassDate classDate2 = new ClassDateImpl2(students);
        classDate2.printAllStudentInfo();
        classDate2.printAllStudentAverageScore();
    }
}
```

### 4. 接口的多继承

**核心概念**：
- 接口与接口之间可以多继承
- 一个接口可以同时继承多个接口
- 实现类只需实现一个接口就相当于实现了多个接口

```java
// B.java - d11_interface_extends
public interface B {
    void b();
}

// C.java - d11_interface_extends
public interface C {
    void c();
}

// A.java - d11_interface_extends
// 接口和接口是多继承的，一个接口可以同时继承多个接口
public interface A extends B, C {
    void a();
}

// D.java - d11_interface_extends
public class D implements A {
    @Override
    public void a() {
        System.out.println("实现a方法");
    }

    @Override
    public void b() {
        System.out.println("实现b方法");
    }

    @Override
    public void c() {
        System.out.println("实现c方法");
    }
}

// Test.java - d11_interface_extends
public class Test {
    public static void main(String[] args) {
        // 接口的多继承可以让实现类只实现一个接口，相当于实现了很多接口
        D d = new D();
        d.a();
        d.b();
        d.c();
    }
}
```

### 5. JDK8接口新特性

**核心概念**：
- JDK8为接口新增了默认方法、静态方法和私有方法
- 解决了接口演进的问题
- 提供了更强大的接口功能

```java
// A.java - d12_interrface_jdk8
public interface A {
    // 默认方法，实例方法，必须用default修饰
    // 默认用public修饰
    public default void run() {
        go();
        System.out.println("run");
    }

    // 私有方法(私有的实例方法) JDK9才有的
    // 只能当前接口内部的默认方法或者私有方法来调用
    private void go() {
        System.out.println("go go go");
    }

    // 静态方法
    // 默认会用public修饰
    // 接口的静态方法必须用接口名本身调用
    static void inAddr() {
        System.out.println("inAddr go go");
    }
}

// B.java - d12_interrface_jdk8
public class B implements A {
    // 可以选择重写默认方法，也可以不重写
}

// Test.java - d12_interrface_jdk8
public class Test {
    public static void main(String[] args) {
        B b = new B();
        b.run();  // 调用默认方法

        A.inAddr();  // 调用静态方法
    }
}
```

## 💡 实践应用总结

### 多态适用场景
- **方法参数**：父类类型接收不同子类对象
- **返回值类型**：方法返回父类类型，实际返回子类对象
- **数组/集合**：存储不同子类对象的统一处理

### Final适用场景
- **工具类**：防止被继承，如Math、String类
- **常量定义**：配置信息、错误码等不可变数据
- **模板方法**：防止核心算法被修改

### 抽象类适用场景
- **模板方法模式**：定义算法骨架，具体步骤由子类实现
- **部分实现**：提供通用功能，强制子类实现特定方法
- **代码复用**：避免重复代码，提供统一接口

### 接口适用场景
- **多重继承**：一个类需要多种能力
- **解耦合设计**：面向接口编程，降低依赖
- **规范定义**：制定标准，不同实现类提供不同策略
- **回调机制**：事件处理、监听器模式

## 🎯 学习检验

通过day02的学习，您应该能够：
1. 理解多态的本质和使用场景，正确进行类型转换
2. 掌握final关键字的各种用法和应用场景
3. 设计和使用抽象类，实现模板方法模式
4. 熟练使用接口编程，实现多重继承和解耦合
5. 理解JDK8接口新特性，灵活运用默认方法和静态方法

这些进阶特性是Java面向对象编程的核心，为后续学习设计模式、框架开发等高级内容奠定坚实基础。

# Java综合练习题 - 随机混合练习

## 📋 练习概述

这套练习题综合运用了你已学习的Java核心知识点，包括面向对象编程、泛型、集合框架、异常处理、内部类、枚举等。通过4个实际场景的练习，帮你巩固和提升Java编程能力。

## 🎯 涵盖的知识点

### 核心概念
- ✅ **面向对象编程**：封装、继承、多态、抽象类
- ✅ **泛型编程**：泛型类、泛型方法、类型安全
- ✅ **集合框架**：List、Map、Stream API
- ✅ **异常处理**：自定义异常、异常传播
- ✅ **内部类**：匿名内部类、函数式接口
- ✅ **枚举类型**：枚举定义、枚举方法

### 高级特性
- ✅ **Lambda表达式**：函数式编程风格
- ✅ **Stream API**：数据流处理、过滤、映射
- ✅ **设计模式**：模板方法模式、策略模式思想

## 📚 练习详解

### 练习1：学生管理系统 🎓
**主要知识点**：枚举、集合操作、Stream API、排序

**核心功能**：
- 学生信息封装（姓名、年龄、年级、成绩）
- 使用枚举表示年级信息
- 学生列表管理和排序
- Stream API进行数据过滤

**技术亮点**：
```java
// 枚举的使用
enum Grade {
    FRESHMAN("大一"), SOPHOMORE("大二"), JUNIOR("大三"), SENIOR("大四");
}

// Stream API过滤
public List<Student> findExcellentStudents() {
    return students.stream()
                  .filter(s -> s.getScore() >= 90)
                  .collect(Collectors.toList());
}
```

### 练习2：图书馆管理 📖
**主要知识点**：异常处理、Map集合、自定义异常

**核心功能**：
- 图书信息管理（标题、作者、类型、借阅状态）
- 借书和还书操作
- 自定义异常处理机制
- Map集合存储图书数据

**技术亮点**：
```java
// 自定义异常
class LibraryException extends Exception {
    public LibraryException(String message) {
        super(message);
    }
}

// 异常处理和业务逻辑
public void borrowBook(String title) throws LibraryException {
    Book book = books.get(title);
    if (book == null) {
        throw new LibraryException("图书不存在：" + title);
    }
    // ... 更多业务逻辑
}
```

### 练习3：员工薪资计算 💰
**主要知识点**：抽象类、继承、多态、Stream聚合

**核心功能**：
- 抽象员工类设计
- 全职员工和兼职员工的不同薪资计算
- 多态在薪资计算中的应用
- Stream API进行薪资统计

**技术亮点**：
```java
// 抽象类和多态
abstract class Employee {
    public abstract double calculateSalary();
}

// Stream聚合操作
double totalSalary = employees.stream()
    .mapToDouble(Employee::calculateSalary)
    .sum();
```

### 练习4：商品订单处理 🛒
**主要知识点**：内部类、函数式接口、复杂对象设计

**核心功能**：
- 订单和订单项的复合关系
- 匿名内部类实现订单验证
- 函数式接口定义
- 订单折扣计算逻辑

**技术亮点**：
```java
// 函数式接口
@FunctionalInterface
interface OrderValidator {
    boolean validate(Order order);
}

// 匿名内部类
private OrderValidator validator = new OrderValidator() {
    @Override
    public boolean validate(Order order) {
        return order.getItems().size() > 0 && order.calculateTotal() > 0;
    }
};
```

## 🚀 运行指南

### 编译和运行
```bash
# 进入Ex目录
cd Ex

# 编译Java文件
javac -d . src/RandomMixedExercises.java

# 运行程序
java src.RandomMixedExercises
```

### 预期输出
程序将依次执行4个练习，输出包括：
1. 学生信息显示和排序结果
2. 图书馆借还书操作结果
3. 员工薪资计算明细
4. 订单处理和折扣应用

## 💡 学习建议

### 代码分析要点
1. **观察设计模式**：注意抽象类、接口的使用场景
2. **理解多态应用**：员工薪资计算中的多态实现
3. **掌握异常处理**：图书馆管理中的异常设计
4. **学习函数式编程**：Stream API和Lambda表达式的使用

### 扩展练习
1. **添加新功能**：为学生管理系统添加按年级分组功能
2. **优化异常处理**：为订单处理添加更详细的异常类型
3. **增强数据验证**：为所有输入数据添加验证逻辑
4. **实现持久化**：将数据保存到文件中

### 进阶挑战
1. **多线程安全**：考虑在多线程环境下的数据安全
2. **性能优化**：分析和优化集合操作的性能
3. **设计模式应用**：尝试应用更多设计模式
4. **单元测试**：为每个类编写单元测试

## 🎯 学习成果检验

完成这套练习后，你应该能够：
- ✅ 熟练运用面向对象的三大特性
- ✅ 正确使用Java集合框架和Stream API
- ✅ 设计和处理自定义异常
- ✅ 理解和应用内部类、枚举等高级特性
- ✅ 编写结构清晰、逻辑完整的Java程序

## 📖 相关知识点复习

如果在练习中遇到困难，建议复习以下内容：
- 面向对象编程基础（day01-day02）
- 泛型和内部类（day03）
- 常用API和集合框架（day04-day08）
- 异常处理机制（day06）
- Stream API应用（day08）

---

**💪 加油！通过这些练习，你的Java编程能力将得到显著提升！**

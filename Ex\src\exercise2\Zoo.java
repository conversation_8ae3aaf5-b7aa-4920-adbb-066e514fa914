package exercise2;

import java.util.List;
import java.util.ArrayList;

/**
 * 练习2：动物园类
 * 
 * 要求实现：
 * 1. 管理动物列表
 * 2. addAnimal()方法
 * 3. showAllAnimals()方法
 * 4. makeAllSounds()方法（体现多态）
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class Zoo {
    
    // TODO: 定义动物列表
    ArrayList<Animal> animals =  new ArrayList<>();
    
    
    // TODO: 构造方法
    public Zoo(){}

    public Zoo(ArrayList<Animal> animals){
        this.animals = animals;
    }
    
    
    // TODO: 添加动物方法
    public void addAnimal(Animal animal) {
        // 你的实现
    }
    
    // TODO: 显示所有动物信息
    public void showAllAnimals() {
       for(Animal animal : animals){
           System.out.println(animal);
       }
    }
    
    // TODO: 让所有动物发声（体现多态）
    public void makeAllSounds() {
        // 你的实现
        for(Animal animal : animals){
            animal.makeSound();
        }
    }
    
    // TODO: 获取动物数量
    public int getAnimalCount() {
        // 你的实现
        return animals.size();
    }
}

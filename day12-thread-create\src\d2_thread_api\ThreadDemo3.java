package d2_thread_api;

public class ThreadDemo3 {
    public static void main(String[] args) throws Exception {
        // 3. 创建线程对象 代表具体的线程
        Thread t = new MyThread2();
        // 4.启动线程  会自动执行run方法
        t.start();
        //不能直接调用run  cpu不会注册新线程执行 此时相当于单线程

        // 主线程执行的任务    主线程要放在启动子线程之后      否则相当于单线程
        for (int i = 0; i < 5; i++) {
            System.out.println("主线程输出：" + i);
            if(i==2){
                t.join();  // 让t 插队先执行完毕
            }
        }
    }
}

//1.定义一个为继承thread类，成为线程类
class MyThread2 extends Thread {
    //2.重写run方法，声明线程要干的事
    @Override
    public void run() {
        for (int i = 0; i < 5; i++) {
            System.out.println("子线程输出：" + i);
        }
    }
}
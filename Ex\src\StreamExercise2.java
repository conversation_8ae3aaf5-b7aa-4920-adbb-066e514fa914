import org.w3c.dom.ls.LSOutput;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Stream流练习题 - 进阶操作
 * 请完成以下练习题，使用Stream流来解决问题
 */
public class StreamExercise2 {
    public static void main(String[] args) {
        // 学生数据
        List<Student> students = Arrays.asList(
            new Student("张三", 20, 85.5),
            new Student("李四", 22, 92.0),
            new Student("王五", 19, 78.5),
            new Student("张六", 21, 88.0),
            new Student("赵七", 23, 95.5),
            new Student("钱八", 20, 82.0),
            new Student("孙九", 24, 90.5),
            new Student("周十", 18, 76.0),
            new Student("张伟", 22, 87.5),
            new Student("李娜", 21, 93.0)
        );
        
        System.out.println("=== Stream流进阶练习题 ===");
        
        // 题目1：找出所有成绩大于90分的学生
        System.out.println("题目1：找出所有成绩大于90分的学生");
        // TODO: 使用Stream流筛选成绩>90的学生，并打印结果
        students.stream().filter( s -> s.getScore() > 90).forEach(System.out::println);


        // 题目2：按成绩降序排列学生
        System.out.println("\n题目2：按成绩降序排列学生");
        // TODO: 使用Stream流按成绩降序排列学生，并打印结果
        students.stream().sorted( (o1,o2) -> Double.compare(o2.getScore(),o1.getScore()) ).forEach(System.out::println);

        
        
        // 题目3：获取所有学生的姓名列表
        System.out.println("\n题目3：获取所有学生的姓名列表");
        // TODO: 使用Stream流提取所有学生的姓名，收集到List中并打印
        students.stream().map(s -> s.getName()).forEach(System.out::println);
        
        // 题目4：计算所有学生的平均成绩
        System.out.println("\n题目4：计算所有学生的平均成绩");
        // TODO: 使用Stream流计算所有学生的平均成绩，并打印结果
        Double sum = 0.0;
        System.out.println(students.stream().mapToDouble(s -> s.getScore()).average().getAsDouble());
        
        // 题目5：找出年龄最大的学生
        System.out.println("\n题目5：找出年龄最大的学生");
        // TODO: 使用Stream流找出年龄最大的学生，并打印结果
        students.stream().max((o1, o2) -> (o1.getAge() - o2.getAge())).ifPresent(System.out::println);


        // 题目6：按年龄分组
        System.out.println("\n题目6：按年龄分组");
        // TODO: 使用Stream流按年龄对学生进行分组，并打印结果
        Map<Integer, List<Student>> students1 =students.stream().collect(Collectors.groupingBy(Student::getAge));

        students1.entrySet().stream().forEach(System.out::println);
        
        // 题目7：统计各年龄段的学生人数
        System.out.println("\n题目7：统计各年龄段的学生人数");
        // TODO: 使用Stream流统计各年龄的学生人数，并打印结果
        
        
        // 题目8：找出成绩前3名的学生
        System.out.println("\n题目8：找出成绩前3名的学生");
        // TODO: 使用Stream流找出成绩前3名的学生，并打印结果
        students.stream().sorted(((o1, o2) ->  Double.compare(o2.getScore(), o1.getScore()))).limit(3).forEach(System.out::println);
        
        
        // 题目9：判断是否所有学生都及格（>=60分）
        System.out.println("\n题目9：判断是否所有学生都及格（>=60分）");
        // TODO: 使用Stream流判断是否所有学生成绩都>=60，并打印结果
        
        
        // 题目10：将学生姓名用逗号连接成字符串
        System.out.println("\n题目10：将学生姓名用逗号连接成字符串");
        // TODO: 使用Stream流将所有学生姓名用逗号连接，并打印结果
        
    }
}

// 学生类
class Student {
    private String name;
    private int age;
    private double score;
    
    public Student(String name, int age, double score) {
        this.name = name;
        this.age = age;
        this.score = score;
    }
    
    public String getName() { return name; }
    public int getAge() { return age; }
    public double getScore() { return score; }
    
    @Override
    public String toString() {
        return String.format("Student{name='%s', age=%d, score=%.1f}", name, age, score);
    }
}

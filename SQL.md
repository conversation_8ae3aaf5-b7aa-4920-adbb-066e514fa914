DDL:数据定义语言,用来定义数据库对象（数据库，表，字段）

DML:数据操作语言，用来对数据库中的数据进行增删改

DQL:数据查询语言，用来查询数据库中的表的记录

DCL:数据控制语言，用来创建数据库用户、控制数据库的访问权限  （数据库管理员才需要）



DDL:

```
DataBase   ->  Schema
--查询当前数据库
show databases;

--查询当前数据库
select database();

--使用或切换数据库
use 数据库名;

--创建数据库
create database [if not exists] 数据库名 [default charset utf8mb4];

--删除数据库
drop database [if exists] 数据库名;
```



# SQL数据类型详解

## 数值类型

### 整数类型
| 类型 | 大小 | 范围（有符号） | 范围（无符号） | 用途 |
|------|------|----------------|----------------|------|
| TINYINT | 1字节 | (-128, 127) | (0, 255) | 小整数值 |
| SMALLINT | 2字节 | (-32768, 32767) | (0, 65535) | 大整数值 |
| MEDIUMINT | 3字节 | (-8388608, 8388607) | (0, 16777215) | 大整数值 |
| INT/INTEGER | 4字节 | (-2147483648, 2147483647) | (0, 4294967295) | 大整数值 |
| BIGINT | 8字节 | (-9223372036854775808, 9223372036854775807) | (0, 18446744073709551615) | 极大整数值 |

### 浮点数类型
| 类型 | 大小 | 精度 | 用途 |
|------|------|------|------|
| FLOAT | 4字节 | 单精度浮点数值 | 小数值 |
| DOUBLE | 8字节 | 双精度浮点数值 | 小数值 |
| DECIMAL(M,D) | 变长 | 精确的小数值 | 金融计算等需要精确小数的场合 |

## 字符串类型

### 定长字符串
| 类型 | 大小 | 用途 |
|------|------|------|
| CHAR(M) | M字节，1<=M<=255 | 定长字符串 |

### 变长字符串
| 类型 | 大小 | 用途 |
|------|------|------|
| VARCHAR(M) | L+1字节，L<=M且1<=M<=65535 | 变长字符串 |
| TINYTEXT | L+1字节，L<2^8 | 短文本字符串 |
| TEXT | L+2字节，L<2^16 | 长文本数据 |
| MEDIUMTEXT | L+3字节，L<2^24 | 中等长度文本数据 |
| LONGTEXT | L+4字节，L<2^32 | 极大文本数据 |

### 二进制类型
| 类型 | 大小 | 用途 |
|------|------|------|
| BINARY(M) | M字节 | 定长二进制字符串 |
| VARBINARY(M) | M+1字节 | 变长二进制字符串 |
| TINYBLOB | L+1字节，L<2^8 | 短二进制字符串 |
| BLOB | L+2字节，L<2^16 | 二进制形式的长文本数据 |
| MEDIUMBLOB | L+3字节，L<2^24 | 二进制形式的中等长度文本数据 |
| LONGBLOB | L+4字节，L<2^32 | 二进制形式的极大文本数据 |

## 日期时间类型

| 类型 | 大小 | 范围 | 格式 | 用途 |
|------|------|------|------|------|
| DATE | 3字节 | 1000-01-01 到 9999-12-31 | YYYY-MM-DD | 日期值 |
| TIME | 3字节 | -838:59:59 到 838:59:59 | HH:MM:SS | 时间值或持续时间 |
| YEAR | 1字节 | 1901 到 2155 | YYYY | 年份值 |
| DATETIME | 8字节 | 1000-01-01 00:00:00 到 9999-12-31 23:59:59 | YYYY-MM-DD HH:MM:SS | 混合日期和时间值 |
| TIMESTAMP | 4字节 | 1970-01-01 00:00:01 UTC 到 2038-01-19 03:14:07 UTC | YYYY-MM-DD HH:MM:SS | 混合日期和时间值，时间戳 |

## 其他类型

### 布尔类型
| 类型 | 说明 |
|------|------|
| BOOLEAN/BOOL | 等价于TINYINT(1)，0表示false，非0表示true |

### 枚举类型
| 类型 | 说明 | 示例 |
|------|------|------|
| ENUM | 枚举类型，只能选择预定义的值之一 | ENUM('small','medium','large') |

### 集合类型
| 类型 | 说明 | 示例 |
|------|------|------|
| SET | 集合类型，可以选择预定义值的零个或多个 | SET('red','green','blue') |

## 数据类型选择建议

### 1. 整数类型选择
```sql
-- 年龄字段：使用TINYINT（0-255足够）
age TINYINT UNSIGNED

-- 用户ID：使用INT，配合AUTO_INCREMENT
id INT AUTO_INCREMENT PRIMARY KEY

-- 大数据量的ID：使用BIGINT
big_id BIGINT AUTO_INCREMENT PRIMARY KEY
```

### 2. 字符串类型选择
```sql
-- 固定长度：使用CHAR
gender CHAR(1)  -- 性别：M/F
country_code CHAR(2)  -- 国家代码：CN/US

-- 变长字符串：使用VARCHAR
username VARCHAR(50)  -- 用户名
email VARCHAR(100)    -- 邮箱

-- 长文本：使用TEXT
description TEXT      -- 描述信息
content LONGTEXT     -- 文章内容
```

### 3. 小数类型选择
```sql
-- 金融相关：使用DECIMAL
price DECIMAL(10,2)   -- 价格：最多8位整数，2位小数
salary DECIMAL(15,2)  -- 工资

-- 科学计算：使用DOUBLE
temperature DOUBLE    -- 温度
coordinates DOUBLE    -- 坐标
```

### 4. 日期时间选择
```sql
-- 只需要日期：使用DATE
birth_date DATE       -- 生日

-- 需要完整时间：使用DATETIME
created_at DATETIME   -- 创建时间

-- 需要时区处理：使用TIMESTAMP
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
```

## 约束示例

```sql
#约束
#非空约束 not null   唯一约束 unique   主键约束primary key   默认约束  default  外键约束 foreign key

use java_db;
create TABLE Users(
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '唯一标识',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    name VARCHAR(20) NOT NULL COMMENT '姓名',
    age TINYINT UNSIGNED COMMENT '年龄',
    gender CHAR(1) DEFAULT 'M' COMMENT '性别',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    salary DECIMAL(10,2) COMMENT '工资',
    birth_date DATE COMMENT '生日',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '用户表';
```


DDL:数据定义语言,用来定义数据库对象（数据库，表，字段）

DML:数据操作语言，用来对数据库中的数据进行增删改

DQL:数据查询语言，用来查询数据库中的表的记录

DCL:数据控制语言，用来创建数据库用户、控制数据库的访问权限  （数据库管理员才需要）



DDL:

## 数据库操作
```sql
-- 数据库相关操作
DataBase   ->  Schema

-- 查询所有数据库
show databases;

-- 查询当前数据库
select database();

-- 使用或切换数据库
use 数据库名;

-- 创建数据库
create database [if not exists] 数据库名 [default charset utf8mb4];

-- 删除数据库
drop database [if exists] 数据库名;
```

## 表操作

### 查询表信息
```sql
-- 查询当前数据库的所有表
show tables;

-- 查询表结构
desc 表名;

-- 查询建表语句
show create table 表名;
```

### 修改表结构 (ALTER TABLE)
```sql
-- 添加字段
alter table 表名 add 字段名 类型(长度) [comment 注释] [约束];

-- 修改字段数据类型
alter table 表名 modify 字段名 新数据类型(长度);

-- 修改字段名与字段类型
alter table 表名 change 旧字段名 新字段名 类型(长度) [comment 注释] [约束];

-- 删除字段
alter table 表名 drop column 字段名;

-- 修改表名
alter table 表名 to 新表名;
```

### 删除表
```sql
-- 删除表
drop table [if exists] 表名;
```

## ALTER TABLE 实际操作示例

### 基于Users表的修改示例
```sql
-- 假设有一个基础的Users表
CREATE TABLE Users(
    id INT PRIMARY KEY,
    name VARCHAR(20),
    age INT
);

-- 1. 添加字段示例
ALTER TABLE Users ADD email VARCHAR(100) COMMENT '邮箱地址';
ALTER TABLE Users ADD phone VARCHAR(20) COMMENT '手机号码';
ALTER TABLE Users ADD created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 2. 修改字段数据类型
ALTER TABLE Users MODIFY age TINYINT UNSIGNED;  -- 将age改为TINYINT类型
ALTER TABLE Users MODIFY name VARCHAR(50);      -- 扩大name字段长度

-- 3. 修改字段名和类型
ALTER TABLE Users CHANGE name username VARCHAR(50) COMMENT '用户名';
ALTER TABLE Users CHANGE phone mobile VARCHAR(15) COMMENT '手机号';

-- 4. 删除字段
ALTER TABLE Users DROP COLUMN mobile;

-- 5. 修改表名
ALTER TABLE Users TO user_info;

-- 6. 添加约束
ALTER TABLE Users ADD CONSTRAINT uk_email UNIQUE(email);
ALTER TABLE Users ADD CONSTRAINT ck_age CHECK(age >= 0 AND age <= 150);
```

### 常用ALTER操作组合
```sql
-- 一次性添加多个字段
ALTER TABLE Users
ADD COLUMN gender CHAR(1) DEFAULT 'M' COMMENT '性别',
ADD COLUMN salary DECIMAL(10,2) COMMENT '工资',
ADD COLUMN status ENUM('active','inactive') DEFAULT 'active' COMMENT '状态';

-- 修改多个字段
ALTER TABLE Users
MODIFY username VARCHAR(100) NOT NULL,
MODIFY email VARCHAR(150) UNIQUE;
```



# MySQL数据类型详解

MySQL中定义数据字段的类型对数据库的优化是非常重要的。MySQL支持多种类型，大致可以分为三类：**数值、日期/时间和字符串(字符)类型**。

## 数值类型

MySQL支持所有标准SQL数值数据类型。这些类型包括严格数值数据类型(INTEGER、SMALLINT、DECIMAL和NUMERIC)，以及近似数值数据类型(FLOAT、REAL和DOUBLE PRECISION)。

### 整数类型
| 类型 | 大小 | 范围（有符号）signed | 范围（无符号）unsigned | 用途 |
|------|------|----------------|----------------|------|
| TINYINT | 1 Bytes | (-128，127) | (0，255) | 小整数值 |
| SMALLINT | 2 Bytes | (-32 768，32 767) | (0，65 535) | 大整数值 |
| MEDIUMINT | 3 Bytes | (-8 388 608，8 388 607) | (0，16 777 215) | 大整数值 |
| INT或INTEGER | 4 Bytes | (-2 147 483 648，2 147 483 647) | (0，4 294 967 295) | 大整数值 |
| BIGINT | 8 Bytes | (-9,223,372,036,854,775,808，9 223 372 036 854 775 807) | (0，18 446 744 073 709 551 615) | 极大整数值 |

### 浮点数类型
| 类型 | 大小 | 范围 | 用途 |
|------|------|------|------|
| FLOAT | 4 Bytes | (-3.402 823 466 E+38，-1.175 494 351 E-38)，0，(1.175 494 351 E-38，3.402 823 466 351 E+38) | 单精度浮点数值 |
| DOUBLE | 8 Bytes | (-1.797 693 134 862 315 7 E+308，-2.225 073 858 507 201 4 E-308)，0，(2.225 073 858 507 201 4 E-308，1.797 693 134 862 315 7 E+308) | 双精度浮点数值 |
| DECIMAL | 对DECIMAL(M,D)，如果M>D，为M+2否则为D+2 | 依赖于M和D的值 | 小数值（精确） |

## 日期和时间类型

表示时间值的日期和时间类型为DATETIME、DATE、TIMESTAMP、TIME和YEAR。每个时间类型有一个有效值范围和一个"零"值，当指定不合法的MySQL不能表示的值时使用"零"值。

| 类型 | 大小(bytes) | 范围 | 格式 | 用途 |
|------|-------------|------|------|------|
| DATE | 3 | 1000-01-01/9999-12-31 | YYYY-MM-DD | 日期值 |
| TIME | 3 | '-838:59:59'/'838:59:59' | HH:MM:SS | 时间值或持续时间 |
| YEAR | 1 | 1901/2155 | YYYY | 年份值 |
| DATETIME | 8 | '1000-01-01 00:00:00' 到 '9999-12-31 23:59:59' | YYYY-MM-DD hh:mm:ss | 混合日期和时间值 |
| TIMESTAMP | 4 | '1970-01-01 00:00:01' UTC 到 '2038-01-19 03:14:07' UTC | YYYY-MM-DD hh:mm:ss | 混合日期和时间值，时间戳 |

## 字符串类型

字符串类型指CHAR、VARCHAR、BINARY、VARBINARY、BLOB、TEXT、ENUM和SET。

| 类型 | 大小 | 用途 |
|------|------|------|
| CHAR | 0-255 bytes | 定长字符串 |
| VARCHAR | 0-65535 bytes | 变长字符串 |
| TINYBLOB | 0-255 bytes | 不超过 255 个字符的二进制字符串 |
| TINYTEXT | 0-255 bytes | 短文本字符串 |
| BLOB | 0-65 535 bytes | 二进制形式的长文本数据 |
| TEXT | 0-65 535 bytes | 长文本数据 |
| MEDIUMBLOB | 0-16 777 215 bytes | 二进制形式的中等长度文本数据 |
| MEDIUMTEXT | 0-16 777 215 bytes | 中等长度文本数据 |
| LONGBLOB | 0-4 294 967 295 bytes | 二进制形式的极大文本数据 |
| LONGTEXT | 0-4 294 967 295 bytes | 极大文本数据 |

### 重要说明：
- **char(n) 和 varchar(n)** 中括号中 n 代表字符的个数，并不代表字节个数
- **CHAR** 和 **VARCHAR** 类似，但它们保存和检索的方式不同
- **BINARY** 和 **VARBINARY** 类似于 CHAR 和 VARCHAR，不同的是它们包含二进制字符串
- **BLOB** 是一个二进制大对象，可以容纳可变数量的数据
- **TEXT** 类型对应 BLOB 类型，可存储的最大长度不同

## 枚举与集合类型

| 类型 | 说明 | 示例 |
|------|------|------|
| ENUM | 枚举类型，用于存储单一值，可以选择一个预定义的集合 | ENUM('small','medium','large') |
| SET | 集合类型，用于存储多个值，可以选择多个预定义的集合 | SET('red','green','blue') |

## 空间数据类型

用于存储空间数据（地理信息、几何图形等）：
- GEOMETRY
- POINT
- LINESTRING
- POLYGON
- MULTIPOINT
- MULTILINESTRING
- MULTIPOLYGON
- GEOMETRYCOLLECTION

## 字符编码说明

### MySQL 5.0 以上版本字符长度：
- **UTF-8**：一个汉字 = 3个字节
- **GBK**：一个汉字 = 2个字节
- **varchar(n)** 表示 n 个字符，无论汉字和英文，MySQL都能存入 n 个字符

### CHAR vs VARCHAR vs TEXT 选择建议：
- **经常变化的字段用 VARCHAR**
- **知道固定长度的用 CHAR**
- **尽量用 VARCHAR**
- **超过 255 字符的只能用 VARCHAR 或者 TEXT**
- **能用 VARCHAR 的地方不用 TEXT**

### FLOAT vs DOUBLE 区别：
- **FLOAT**：单精度，4字节，最多7位十进制有效数字
- **DOUBLE**：双精度，8字节，最多15-16位十进制有效数字

## 数据类型选择建议

### 1. 整数类型选择
```sql
-- 年龄字段：使用TINYINT（0-255足够）
age TINYINT UNSIGNED

-- 用户ID：使用INT，配合AUTO_INCREMENT
id INT AUTO_INCREMENT PRIMARY KEY

-- 大数据量的ID：使用BIGINT
big_id BIGINT AUTO_INCREMENT PRIMARY KEY
```

### 2. 字符串类型选择
```sql
-- 固定长度：使用CHAR
gender CHAR(1)  -- 性别：M/F
country_code CHAR(2)  -- 国家代码：CN/US

-- 变长字符串：使用VARCHAR
username VARCHAR(50)  -- 用户名
email VARCHAR(100)    -- 邮箱

-- 长文本：使用TEXT
description TEXT      -- 描述信息
content LONGTEXT     -- 文章内容
```

### 3. 小数类型选择
```sql
-- 金融相关：使用DECIMAL（精确计算）
price DECIMAL(10,2)   -- 价格：最多8位整数，2位小数
salary DECIMAL(15,2)  -- 工资

-- 科学计算：使用DOUBLE（近似计算）
temperature DOUBLE    -- 温度
coordinates DOUBLE    -- 坐标
```

### 4. 日期时间选择
```sql
-- 只需要日期：使用DATE
birth_date DATE       -- 生日

-- 需要完整时间：使用DATETIME
created_at DATETIME   -- 创建时间

-- 需要时区处理：使用TIMESTAMP
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
```

## 实际应用示例

### 完整的用户表设计
```sql
-- 约束类型说明：
-- 非空约束 NOT NULL   唯一约束 UNIQUE   主键约束 PRIMARY KEY
-- 默认约束 DEFAULT    外键约束 FOREIGN KEY

USE java_db;

CREATE TABLE Users(
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '唯一标识',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    name VARCHAR(20) NOT NULL COMMENT '姓名',
    age TINYINT UNSIGNED COMMENT '年龄',
    gender CHAR(1) DEFAULT 'M' COMMENT '性别：M/F',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    salary DECIMAL(10,2) COMMENT '工资',
    birth_date DATE COMMENT '生日',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '状态',
    hobbies SET('reading', 'sports', 'music', 'travel') COMMENT '爱好',
    avatar BLOB COMMENT '头像',
    description TEXT COMMENT '个人描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '用户表' ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 数据类型使用说明
```sql
-- 查看表结构
DESC Users;

-- 查看字段长度（字节数）
SELECT LENGTH(name) as name_bytes, CHAR_LENGTH(name) as name_chars FROM Users;

-- 插入测试数据
INSERT INTO Users (username, name, age, gender, email, birth_date, hobbies)
VALUES ('zhangsan', '张三', 25, 'M', '<EMAIL>', '1999-01-01', 'reading,sports');
```

---

# DQL: 数据查询语言

DQL（Data Query Language）用来查询数据库中表的记录。

## 基本查询语法

```sql
SELECT 字段列表
FROM 表名列表
WHERE 条件列表
GROUP BY 分组字段列表
HAVING 分组后条件列表
ORDER BY 排序字段列表
LIMIT 分页参数;
```

## 员工表示例数据

基于黑马程序员的员工表结构和数据：

```sql
-- 员工表结构
CREATE TABLE emp(
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT 'ID,主键',
    username VARCHAR(20) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(32) NOT NULL COMMENT '密码',
    name VARCHAR(10) NOT NULL COMMENT '姓名',
    gender TINYINT UNSIGNED NOT NULL COMMENT '性别, 1:男, 2:女',
    phone CHAR(11) NOT NULL UNIQUE COMMENT '手机号',
    job TINYINT UNSIGNED COMMENT '职位, 1:班主任,2:讲师,3:学工主管,4:教研主管,5:咨询师',
    salary INT UNSIGNED COMMENT '薪资',
    image VARCHAR(300) COMMENT '头像',
    entry_date DATE COMMENT '入职日期',
    create_time DATETIME COMMENT '创建时间',
    update_time DATETIME COMMENT '修改时间'
) COMMENT '员工表';
```

## 基础查询

### 1. 查询所有字段
```sql
-- 查询所有员工信息
SELECT * FROM emp;

-- 查询指定字段
SELECT name, salary, job FROM emp;
```

### 2. 字段别名
```sql
-- 使用别名
SELECT name AS 姓名, salary AS 薪资 FROM emp;
SELECT name 姓名, salary 薪资 FROM emp;
```

### 3. 去除重复记录
```sql
-- 查询所有职位（去重）
SELECT DISTINCT job FROM emp;
```


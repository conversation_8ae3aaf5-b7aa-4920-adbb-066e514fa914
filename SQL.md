DDL:数据定义语言,用来定义数据库对象（数据库，表，字段）

DML:数据操作语言，用来对数据库中的数据进行增删改

DQL:数据查询语言，用来查询数据库中的表的记录

DCL:数据控制语言，用来创建数据库用户、控制数据库的访问权限  （数据库管理员才需要）



DDL:

```
DataBase   ->  Schema
--查询当前数据库
show databases;

--查询当前数据库
select database();

--使用或切换数据库
use 数据库名;

--创建数据库
create database [if not exists] 数据库名 [default charset utf8mb4];

--删除数据库
drop database [if exists] 数据库名;
```



```
#约束
#非空约束 not null   唯一约束 unique   主键约束primary key   默认约束  default  外键约束 foreign key

use java_db;
create TABLE Users(
    id int comment  '唯一标识',
    username Varchar(20) comment '用户名',
    name varchar(20) comment '姓名',
    age int comment '年龄',
    gender char(1) comment '性别'
)comment "用户表";
```


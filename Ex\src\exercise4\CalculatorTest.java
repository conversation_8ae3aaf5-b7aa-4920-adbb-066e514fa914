package exercise4;

/**
 * 练习4：计算器测试程序
 * 
 * 测试要求：
 * 1. 测试各种正常和异常情况
 * 2. 使用try-catch处理异常
 * 3. 输出友好的错误提示信息
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class CalculatorTest {
    
    public static void main(String[] args) throws Exception {
        System.out.println("=== 安全计算器测试 ===\n");
        
        // TODO: 创建计算器对象
        Calculator calculator = new Calculator();
        
        
        // TODO: 测试正常的四则运算
        System.out.println("--- 正常运算测试 ---");

            calculator.calculate(10,2,"*");
            calculator.calculate(10,2,"+");
            calculator.calculate(10,2,"-");
            calculator.calculate(10,2,"/");


        // TODO: 测试除零异常
        System.out.println("\n--- 除零异常测试 ---");

        calculator.calculate(10,2,"0");
        
        // TODO: 测试无效操作符异常
        System.out.println("\n--- 无效操作符测试 ---");
        System.out.println(calculator.calculate(10,2,"+++"));
        
        
        // TODO: 测试通用计算方法
        System.out.println("\n--- 通用计算方法测试 ---");
        
        
        // TODO: 边界值测试
        System.out.println("\n--- 边界值测试 ---");
        
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    // TODO: 可以添加辅助测试方法
    
}

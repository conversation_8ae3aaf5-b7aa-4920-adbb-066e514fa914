package d12_Objects;

import d11_Object.Student;

import java.util.Objects;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 17:05
 **/
public class Test {
    public static void main(String[] args) {
        Student s1 = null;
        Student s2 = new Student("2",12,100);
        //System.out.println(s1.equals(s2));    //对象主调null  直接爆 空指针异常

        /*public static boolean equals(Object a, Object b) {
            return (a == b) || (a != null && a.equals(b));
        }    <--源码  */
        System.out.println(Objects.equals(s1,s2));   // 更安全 可靠
    }
}
# Java常用方法参考表

## 📋 目录
- [字符串处理](#字符串处理)
- [集合框架](#集合框架)
- [Stream流操作](#stream流操作)
- [文件与IO操作](#文件与io操作)
- [异常处理](#异常处理)
- [线程编程](#线程编程)
- [数学运算](#数学运算)
- [时间日期](#时间日期)
- [正则表达式](#正则表达式)
- [Lambda表达式](#lambda表达式)
- [工具类方法](#工具类方法)

---

## 字符串处理

### StringBuilder - 高效字符串构建

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `StringBuilder()` | 创建空的StringBuilder | `StringBuilder sb = new StringBuilder();` | 默认容量16字符 |
| `StringBuilder(String str)` | 用指定字符串初始化 | `StringBuilder sb = new StringBuilder("Hello");` | 初始容量为字符串长度+16 |
| `append(Object obj)` | 追加内容，支持链式调用 | `sb.append("Hello").append(123).append(true);` | 返回StringBuilder对象 |
| `reverse()` | 反转字符串内容 | `sb.reverse();` | 直接修改原对象 |
| `length()` | 获取字符串长度 | `int len = sb.length();` | 返回当前字符数 |
| `toString()` | 转换为String对象 | `String result = sb.toString();` | 常用于最终输出 |

**实际应用场景示例：**
```java
// 场景1: 构建SQL查询语句
StringBuilder sql = new StringBuilder("SELECT * FROM users WHERE 1=1");
if (name != null) {
    sql.append(" AND name = '").append(name).append("'");
}
if (age > 0) {
    sql.append(" AND age = ").append(age);
}
String query = sql.toString();

// 场景2: 生成HTML内容
StringBuilder html = new StringBuilder();
html.append("<table>")
    .append("<tr><th>姓名</th><th>年龄</th></tr>");
for (User user : users) {
    html.append("<tr>")
        .append("<td>").append(user.getName()).append("</td>")
        .append("<td>").append(user.getAge()).append("</td>")
        .append("</tr>");
}
html.append("</table>");

// 场景3: 日志信息拼接
StringBuilder log = new StringBuilder();
log.append("[").append(LocalDateTime.now()).append("] ")
   .append("用户 ").append(username).append(" 执行了 ").append(operation)
   .append(" 操作，耗时 ").append(duration).append("ms");
logger.info(log.toString());
```

### StringJoiner - 便捷字符串连接

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `StringJoiner(String delimiter)` | 创建指定分隔符的连接器 | `StringJoiner sj = new StringJoiner(",");` | 只有分隔符 |
| `StringJoiner(String delimiter, String prefix, String suffix)` | 创建带前后缀的连接器 | `StringJoiner sj = new StringJoiner(",", "[", "]");` | 完整格式控制 |
| `add(String str)` | 添加字符串元素 | `sj.add("apple").add("banana");` | 支持链式调用 |
| `toString()` | 获取连接结果 | `String result = sj.toString();` | 输出格式化字符串 |

**实际应用场景示例：**
```java
// 场景1: 生成CSV格式数据
StringJoiner csvRow = new StringJoiner(",");
csvRow.add("张三").add("25").add("软件工程师").add("北京");
String csv = csvRow.toString(); // "张三,25,软件工程师,北京"

// 场景2: 构建JSON数组格式
StringJoiner jsonArray = new StringJoiner(",", "[", "]");
for (String item : items) {
    jsonArray.add("\"" + item + "\"");
}
String json = jsonArray.toString(); // ["item1","item2","item3"]

// 场景3: 生成SQL IN子句
StringJoiner inClause = new StringJoiner(",", "(", ")");
for (Integer id : userIds) {
    inClause.add(id.toString());
}
String sql = "SELECT * FROM users WHERE id IN " + inClause.toString();
// SELECT * FROM users WHERE id IN (1,2,3,4)

// 场景4: 路径拼接
StringJoiner pathJoiner = new StringJoiner("/");
pathJoiner.add("home").add("user").add("documents").add("file.txt");
String path = pathJoiner.toString(); // "home/user/documents/file.txt"
```

### String常用方法

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `matches(String regex)` | 正则表达式匹配 | `"abc123".matches("\\w+");` | 整个字符串匹配 |
| `replaceAll(String regex, String replacement)` | 正则替换 | `"abc123def".replaceAll("\\d+", "XXX");` | 全局替换 |
| `split(String regex)` | 正则分割 | `"a,b,c".split(",");` | 返回字符串数组 |
| `substring(int beginIndex)` | 截取子字符串 | `"Hello".substring(1);` | 从指定位置到末尾 |
| `substring(int beginIndex, int endIndex)` | 截取子字符串 | `"Hello".substring(1, 4);` | 指定起始和结束位置 |
| `toLowerCase()` | 转换为小写 | `"HELLO".toLowerCase();` | 返回新字符串 |
| `toUpperCase()` | 转换为大写 | `"hello".toUpperCase();` | 返回新字符串 |
| `trim()` | 去除首尾空白 | `" hello ".trim();` | 返回新字符串 |

**实际应用场景示例：**
```java
// 场景1: 数据验证
public boolean isValidEmail(String email) {
    return email.matches("^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$");
}

public boolean isValidPhone(String phone) {
    return phone.matches("^1[3-9]\\d{9}$"); // 中国手机号
}

// 场景2: 数据清洗和格式化
public String cleanUserInput(String input) {
    if (input == null) return "";
    return input.trim()                    // 去除首尾空白
                .replaceAll("\\s+", " ")   // 多个空格替换为单个
                .replaceAll("[^\\w\\s]", ""); // 移除特殊字符
}

// 场景3: 文件路径处理
public String getFileExtension(String fileName) {
    if (fileName.contains(".")) {
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
    }
    return "";
}

// 场景4: 敏感信息脱敏
public String maskPhoneNumber(String phone) {
    if (phone.length() == 11) {
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
    return phone;
}

// 场景5: 解析配置文件
public Map<String, String> parseConfig(String configLine) {
    Map<String, String> config = new HashMap<>();
    String[] pairs = configLine.split(";");
    for (String pair : pairs) {
        String[] keyValue = pair.split("=");
        if (keyValue.length == 2) {
            config.put(keyValue[0].trim(), keyValue[1].trim());
        }
    }
    return config;
}

// 场景6: 字符串格式转换
public String toCamelCase(String snakeCase) {
    String[] words = snakeCase.toLowerCase().split("_");
    StringBuilder result = new StringBuilder(words[0]);
    for (int i = 1; i < words.length; i++) {
        result.append(words[i].substring(0, 1).toUpperCase())
              .append(words[i].substring(1));
    }
    return result.toString();
}
```

---

## 集合框架

### Collection接口 - 单列集合根接口

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `add(E e)` | 添加元素 | `collection.add("element");` | 返回boolean |
| `remove(Object o)` | 删除指定元素 | `collection.remove("element");` | 删除第一个匹配的元素 |
| `contains(Object o)` | 判断是否包含元素 | `boolean exists = collection.contains("element");` | 使用equals方法比较 |
| `size()` | 获取元素个数 | `int count = collection.size();` | 返回集合大小 |
| `isEmpty()` | 判断是否为空 | `boolean empty = collection.isEmpty();` | size() == 0 |
| `clear()` | 清空集合 | `collection.clear();` | 删除所有元素 |
| `toArray()` | 转换为数组 | `Object[] array = collection.toArray();` | 返回Object数组 |

**实际应用场景示例：**
```java
// 场景1: 购物车管理
public class ShoppingCart {
    private Set<Product> products = new HashSet<>();

    public void addProduct(Product product) {
        if (products.add(product)) {
            System.out.println("商品已添加到购物车");
        } else {
            System.out.println("商品已在购物车中");
        }
    }

    public boolean hasProduct(Product product) {
        return products.contains(product);
    }

    public int getProductCount() {
        return products.size();
    }

    public void clearCart() {
        products.clear();
        System.out.println("购物车已清空");
    }
}

// 场景2: 权限管理
public class UserPermissions {
    private Set<String> permissions = new HashSet<>();

    public void grantPermission(String permission) {
        permissions.add(permission);
    }

    public boolean hasPermission(String permission) {
        return permissions.contains(permission);
    }

    public String[] getAllPermissions() {
        return permissions.toArray(new String[0]);
    }
}
```

### List接口 - 有序可重复集合

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `get(int index)` | 根据索引获取元素 | `String item = list.get(0);` | 索引从0开始 |
| `set(int index, E element)` | 修改指定位置元素 | `list.set(1, "newValue");` | 返回原来的元素 |
| `add(int index, E element)` | 在指定位置插入元素 | `list.add(1, "inserted");` | 后续元素后移 |
| `remove(int index)` | 删除指定位置元素 | `String removed = list.remove(0);` | 返回被删除的元素 |
| `indexOf(Object o)` | 查找元素首次出现位置 | `int index = list.indexOf("target");` | 未找到返回-1 |
| `lastIndexOf(Object o)` | 查找元素最后出现位置 | `int index = list.lastIndexOf("target");` | 未找到返回-1 |

**实际应用场景示例：**
```java
// 场景1: 学生成绩管理
public class GradeManager {
    private List<Double> grades = new ArrayList<>();

    public void addGrade(double grade) {
        grades.add(grade);
    }

    public void updateGrade(int index, double newGrade) {
        if (index >= 0 && index < grades.size()) {
            grades.set(index, newGrade);
        }
    }

    public double getGrade(int index) {
        return grades.get(index);
    }

    public double getAverage() {
        return grades.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
    }

    public int findGradePosition(double grade) {
        return grades.indexOf(grade);
    }
}

// 场景2: 任务队列管理
public class TaskQueue {
    private List<Task> tasks = new ArrayList<>();

    public void addTask(Task task) {
        tasks.add(task);
    }

    public void addUrgentTask(Task task) {
        tasks.add(0, task); // 插入到队列头部
    }

    public Task getNextTask() {
        if (!tasks.isEmpty()) {
            return tasks.remove(0); // 获取并移除第一个任务
        }
        return null;
    }

    public Task peekNextTask() {
        return tasks.isEmpty() ? null : tasks.get(0); // 只查看不移除
    }

    public int getTaskCount() {
        return tasks.size();
    }
}

// 场景3: 历史记录管理
public class BrowserHistory {
    private List<String> history = new ArrayList<>();
    private int currentIndex = -1;

    public void visit(String url) {
        // 删除当前位置之后的所有历史记录
        if (currentIndex < history.size() - 1) {
            history = history.subList(0, currentIndex + 1);
        }
        history.add(url);
        currentIndex = history.size() - 1;
    }

    public String back() {
        if (currentIndex > 0) {
            currentIndex--;
            return history.get(currentIndex);
        }
        return null;
    }

    public String forward() {
        if (currentIndex < history.size() - 1) {
            currentIndex++;
            return history.get(currentIndex);
        }
        return null;
    }
}
```

### Map接口 - 键值对集合

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `put(K key, V value)` | 添加键值对 | `map.put("name", "张三");` | 键重复会覆盖值 |
| `get(Object key)` | 根据键获取值 | `String name = map.get("name");` | 键不存在返回null |
| `remove(Object key)` | 删除指定键的映射 | `String removed = map.remove("name");` | 返回被删除的值 |
| `containsKey(Object key)` | 判断是否包含指定键 | `boolean exists = map.containsKey("name");` | 键存在性检查 |
| `containsValue(Object value)` | 判断是否包含指定值 | `boolean exists = map.containsValue("张三");` | 值存在性检查 |
| `keySet()` | 获取所有键的集合 | `Set<String> keys = map.keySet();` | 返回Set集合 |
| `values()` | 获取所有值的集合 | `Collection<String> values = map.values();` | 返回Collection |
| `entrySet()` | 获取键值对集合 | `Set<Map.Entry<K,V>> entries = map.entrySet();` | 用于遍历 |

**实际应用场景示例：**
```java
// 场景1: 用户会话管理
public class SessionManager {
    private Map<String, UserSession> sessions = new ConcurrentHashMap<>();

    public void createSession(String sessionId, UserSession session) {
        sessions.put(sessionId, session);
    }

    public UserSession getSession(String sessionId) {
        return sessions.get(sessionId);
    }

    public boolean isValidSession(String sessionId) {
        return sessions.containsKey(sessionId) &&
               !sessions.get(sessionId).isExpired();
    }

    public void removeSession(String sessionId) {
        sessions.remove(sessionId);
    }

    public int getActiveSessionCount() {
        return sessions.size();
    }
}

// 场景2: 配置管理
public class ConfigManager {
    private Map<String, String> config = new HashMap<>();

    public void loadConfig(Properties props) {
        for (String key : props.stringPropertyNames()) {
            config.put(key, props.getProperty(key));
        }
    }

    public String getConfig(String key, String defaultValue) {
        return config.getOrDefault(key, defaultValue);
    }

    public void updateConfig(String key, String value) {
        config.put(key, value);
    }

    public Set<String> getAllConfigKeys() {
        return config.keySet();
    }
}

// 场景3: 缓存实现
public class SimpleCache<K, V> {
    private Map<K, CacheEntry<V>> cache = new HashMap<>();
    private long ttl; // 生存时间

    public SimpleCache(long ttl) {
        this.ttl = ttl;
    }

    public void put(K key, V value) {
        cache.put(key, new CacheEntry<>(value, System.currentTimeMillis() + ttl));
    }

    public V get(K key) {
        CacheEntry<V> entry = cache.get(key);
        if (entry != null && !entry.isExpired()) {
            return entry.getValue();
        }
        cache.remove(key); // 移除过期条目
        return null;
    }

    public void clearExpired() {
        cache.entrySet().removeIf(entry -> entry.getValue().isExpired());
    }
}

// 场景4: 统计计数
public class WordCounter {
    private Map<String, Integer> wordCount = new HashMap<>();

    public void addWord(String word) {
        wordCount.put(word, wordCount.getOrDefault(word, 0) + 1);
    }

    public int getWordCount(String word) {
        return wordCount.getOrDefault(word, 0);
    }

    public String getMostFrequentWord() {
        return wordCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
    }

    public void printStatistics() {
        wordCount.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry ->
                    System.out.println(entry.getKey() + ": " + entry.getValue()));
    }
}
```

### Collections工具类

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `addAll(Collection<T> c, T... elements)` | 批量添加元素 | `Collections.addAll(list, "a", "b", "c");` | 可变参数 |
| `shuffle(List<?> list)` | 随机打乱集合顺序 | `Collections.shuffle(list);` | 只适用于List |
| `sort(List<T> list)` | 对集合排序 | `Collections.sort(list);` | 元素需实现Comparable |
| `sort(List<T> list, Comparator<T> c)` | 自定义排序 | `Collections.sort(list, comparator);` | 使用比较器 |

---

## Stream流操作

### Stream创建

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `collection.stream()` | 从集合创建流 | `List<String> list = Arrays.asList("a", "b"); Stream<String> stream = list.stream();` | 最常用方式 |
| `Arrays.stream(array)` | 从数组创建流 | `Stream<String> stream = Arrays.stream(new String[]{"a", "b"});` | 数组转流 |
| `Stream.of(T... values)` | 从可变参数创建流 | `Stream<String> stream = Stream.of("a", "b", "c");` | 直接创建 |
| `Stream.generate(Supplier<T> s)` | 生成无限流 | `Stream<Double> randoms = Stream.generate(Math::random);` | 需要limit限制 |

### 中间操作

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `filter(Predicate<T> predicate)` | 过滤元素 | `stream.filter(s -> s.length() > 3)` | 返回满足条件的元素 |
| `map(Function<T, R> mapper)` | 元素转换 | `stream.map(String::toUpperCase)` | 一对一转换 |
| `flatMap(Function<T, Stream<R>> mapper)` | 扁平化映射 | `stream.flatMap(s -> Arrays.stream(s.split("")))` | 一对多转换 |
| `distinct()` | 去重 | `stream.distinct()` | 基于equals方法 |
| `sorted()` | 排序 | `stream.sorted()` | 自然排序 |
| `sorted(Comparator<T> comparator)` | 自定义排序 | `stream.sorted(Comparator.comparing(String::length))` | 使用比较器 |
| `limit(long maxSize)` | 限制元素数量 | `stream.limit(10)` | 取前n个元素 |
| `skip(long n)` | 跳过元素 | `stream.skip(5)` | 跳过前n个元素 |

### 终端操作

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `forEach(Consumer<T> action)` | 遍历元素 | `stream.forEach(System.out::println)` | 无返回值 |
| `collect(Collector<T, A, R> collector)` | 收集结果 | `List<String> result = stream.collect(Collectors.toList())` | 最常用收集方式 |
| `reduce(BinaryOperator<T> accumulator)` | 归约操作 | `Optional<String> result = stream.reduce((a, b) -> a + b)` | 返回Optional |
| `count()` | 计算元素数量 | `long count = stream.count()` | 返回long |
| `anyMatch(Predicate<T> predicate)` | 任意匹配 | `boolean hasLong = stream.anyMatch(s -> s.length() > 5)` | 有一个满足即true |
| `allMatch(Predicate<T> predicate)` | 全部匹配 | `boolean allLong = stream.allMatch(s -> s.length() > 3)` | 全部满足才true |
| `noneMatch(Predicate<T> predicate)` | 无匹配 | `boolean noShort = stream.noneMatch(s -> s.length() < 2)` | 全部不满足才true |
| `findFirst()` | 查找第一个元素 | `Optional<String> first = stream.findFirst()` | 返回Optional |
| `findAny()` | 查找任意元素 | `Optional<String> any = stream.findAny()` | 并行流中有用 |

### Collectors收集器

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `toList()` | 收集为List | `List<String> list = stream.collect(Collectors.toList())` | 最常用 |
| `toSet()` | 收集为Set | `Set<String> set = stream.collect(Collectors.toSet())` | 自动去重 |
| `toMap(Function keyMapper, Function valueMapper)` | 收集为Map | `Map<String, Integer> map = stream.collect(Collectors.toMap(s -> s, String::length))` | 键值映射 |
| `joining()` | 连接字符串 | `String result = stream.collect(Collectors.joining())` | 无分隔符连接 |
| `joining(CharSequence delimiter)` | 带分隔符连接 | `String result = stream.collect(Collectors.joining(","))` | 指定分隔符 |
| `groupingBy(Function classifier)` | 分组 | `Map<Integer, List<String>> grouped = stream.collect(Collectors.groupingBy(String::length))` | 按条件分组 |
| `partitioningBy(Predicate predicate)` | 分区 | `Map<Boolean, List<String>> partitioned = stream.collect(Collectors.partitioningBy(s -> s.length() > 3))` | 二分分组 |

**Stream流操作实际应用场景示例：**
```java
// 场景1: 员工数据处理
public class EmployeeAnalyzer {
    private List<Employee> employees;

    // 获取高薪员工列表
    public List<Employee> getHighSalaryEmployees(double threshold) {
        return employees.stream()
                .filter(emp -> emp.getSalary() > threshold)
                .sorted(Comparator.comparing(Employee::getSalary).reversed())
                .collect(Collectors.toList());
    }

    // 按部门分组统计平均薪资
    public Map<String, Double> getAverageSalaryByDepartment() {
        return employees.stream()
                .collect(Collectors.groupingBy(
                    Employee::getDepartment,
                    Collectors.averagingDouble(Employee::getSalary)
                ));
    }

    // 获取所有员工姓名，逗号分隔
    public String getAllEmployeeNames() {
        return employees.stream()
                .map(Employee::getName)
                .sorted()
                .collect(Collectors.joining(", "));
    }

    // 查找特定技能的员工
    public List<Employee> findEmployeesWithSkill(String skill) {
        return employees.stream()
                .filter(emp -> emp.getSkills().contains(skill))
                .collect(Collectors.toList());
    }
}

// 场景2: 订单数据分析
public class OrderAnalyzer {
    private List<Order> orders;

    // 计算总销售额
    public double getTotalSales() {
        return orders.stream()
                .mapToDouble(Order::getAmount)
                .sum();
    }

    // 获取最近N天的订单
    public List<Order> getRecentOrders(int days) {
        LocalDate cutoffDate = LocalDate.now().minusDays(days);
        return orders.stream()
                .filter(order -> order.getDate().isAfter(cutoffDate))
                .sorted(Comparator.comparing(Order::getDate).reversed())
                .collect(Collectors.toList());
    }

    // 按客户分组统计订单数量
    public Map<String, Long> getOrderCountByCustomer() {
        return orders.stream()
                .collect(Collectors.groupingBy(
                    Order::getCustomerId,
                    Collectors.counting()
                ));
    }

    // 查找大额订单（金额超过阈值）
    public boolean hasLargeOrders(double threshold) {
        return orders.stream()
                .anyMatch(order -> order.getAmount() > threshold);
    }
}

// 场景3: 文本数据处理
public class TextProcessor {

    // 统计单词频率
    public Map<String, Long> getWordFrequency(String text) {
        return Arrays.stream(text.toLowerCase().split("\\W+"))
                .filter(word -> !word.isEmpty())
                .collect(Collectors.groupingBy(
                    Function.identity(),
                    Collectors.counting()
                ));
    }

    // 获取最长的N个单词
    public List<String> getLongestWords(String text, int count) {
        return Arrays.stream(text.split("\\W+"))
                .filter(word -> !word.isEmpty())
                .distinct()
                .sorted(Comparator.comparing(String::length).reversed())
                .limit(count)
                .collect(Collectors.toList());
    }

    // 检查是否包含特定模式的单词
    public boolean containsPattern(List<String> words, String pattern) {
        return words.stream()
                .anyMatch(word -> word.matches(pattern));
    }
}

// 场景4: 数据转换和清洗
public class DataCleaner {

    // 清洗和转换用户输入数据
    public List<String> cleanAndValidateEmails(List<String> rawEmails) {
        return rawEmails.stream()
                .filter(Objects::nonNull)
                .map(String::trim)
                .map(String::toLowerCase)
                .filter(email -> email.matches("^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$"))
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    // 数字数据统计
    public OptionalDouble getAverageOfValidNumbers(List<String> numberStrings) {
        return numberStrings.stream()
                .filter(Objects::nonNull)
                .map(String::trim)
                .filter(s -> s.matches("-?\\d+(\\.\\d+)?"))
                .mapToDouble(Double::parseDouble)
                .average();
    }

    // 分组处理不同类型的数据
    public Map<String, List<String>> categorizeData(List<String> data) {
        return data.stream()
                .filter(Objects::nonNull)
                .filter(s -> !s.trim().isEmpty())
                .collect(Collectors.groupingBy(this::categorizeItem));
    }

    private String categorizeItem(String item) {
        if (item.matches("\\d+")) return "数字";
        if (item.matches("^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$")) return "邮箱";
        if (item.matches("^1[3-9]\\d{9}$")) return "手机号";
        return "其他";
    }
}
```

---

## 文件与IO操作

### File类 - 文件系统抽象

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `File(String pathname)` | 创建File对象 | `File file = new File("path/to/file.txt");` | 路径可以不存在 |
| `exists()` | 判断文件/目录是否存在 | `boolean exists = file.exists();` | 检查实际存在性 |
| `isFile()` | 判断是否为文件 | `boolean isFile = file.isFile();` | 存在且为文件返回true |
| `isDirectory()` | 判断是否为目录 | `boolean isDir = file.isDirectory();` | 存在且为目录返回true |
| `getName()` | 获取文件/目录名称 | `String name = file.getName();` | 包含扩展名 |
| `length()` | 获取文件大小(字节) | `long size = file.length();` | 目录返回0 |
| `lastModified()` | 获取最后修改时间 | `long time = file.lastModified();` | 返回时间戳 |
| `createNewFile()` | 创建新文件 | `boolean created = file.createNewFile();` | 文件不存在时创建 |
| `mkdir()` | 创建单级目录 | `boolean created = file.mkdir();` | 只能创建一级 |
| `mkdirs()` | 创建多级目录 | `boolean created = file.mkdirs();` | 可创建多级目录 |
| `delete()` | 删除文件/空目录 | `boolean deleted = file.delete();` | 不进回收站 |
| `listFiles()` | 获取目录下文件数组 | `File[] files = dir.listFiles();` | 返回File数组 |

### 字节流操作

| 类名/方法签名 | 功能描述 | 使用示例 | 注意事项 |
|-------------|---------|---------|---------|
| `FileInputStream(String name)` | 创建文件字节输入流 | `InputStream is = new FileInputStream("file.txt");` | 文件不存在会抛异常 |
| `FileOutputStream(String name)` | 创建文件字节输出流 | `OutputStream os = new FileOutputStream("file.txt");` | 会覆盖原文件 |
| `FileOutputStream(String name, boolean append)` | 创建追加模式输出流 | `OutputStream os = new FileOutputStream("file.txt", true);` | append=true追加写入 |
| `read()` | 读取单个字节 | `int b = is.read();` | 返回-1表示结束 |
| `read(byte[] b)` | 读取字节数组 | `int len = is.read(buffer);` | 返回实际读取字节数 |
| `write(int b)` | 写入单个字节 | `os.write(65);` | 写入字节值 |
| `write(byte[] b, int off, int len)` | 写入字节数组片段 | `os.write(buffer, 0, len);` | 指定起始位置和长度 |
| `close()` | 关闭流释放资源 | `is.close();` | 必须调用释放资源 |

### 缓冲流

| 类名/方法签名 | 功能描述 | 使用示例 | 注意事项 |
|-------------|---------|---------|---------|
| `BufferedInputStream(InputStream in)` | 创建缓冲字节输入流 | `BufferedInputStream bis = new BufferedInputStream(is);` | 提高读取效率 |
| `BufferedOutputStream(OutputStream out)` | 创建缓冲字节输出流 | `BufferedOutputStream bos = new BufferedOutputStream(os);` | 提高写入效率 |
| `BufferedReader(Reader in)` | 创建缓冲字符输入流 | `BufferedReader br = new BufferedReader(fr);` | 可按行读取 |
| `BufferedWriter(Writer out)` | 创建缓冲字符输出流 | `BufferedWriter bw = new BufferedWriter(fw);` | 可按行写入 |
| `readLine()` | 读取一行文本 | `String line = br.readLine();` | 返回null表示结束 |
| `newLine()` | 写入行分隔符 | `bw.newLine();` | 跨平台换行 |

**文件与IO操作实际应用场景示例：**
```java
// 场景1: 配置文件管理
public class ConfigFileManager {
    private String configPath;

    public ConfigFileManager(String configPath) {
        this.configPath = configPath;
    }

    // 读取配置文件
    public Properties loadConfig() throws IOException {
        Properties props = new Properties();
        File configFile = new File(configPath);

        if (!configFile.exists()) {
            // 创建默认配置文件
            createDefaultConfig();
        }

        try (FileInputStream fis = new FileInputStream(configFile)) {
            props.load(fis);
        }
        return props;
    }

    // 保存配置文件
    public void saveConfig(Properties props) throws IOException {
        File configFile = new File(configPath);
        configFile.getParentFile().mkdirs(); // 确保目录存在

        try (FileOutputStream fos = new FileOutputStream(configFile)) {
            props.store(fos, "Application Configuration");
        }
    }

    private void createDefaultConfig() throws IOException {
        Properties defaultProps = new Properties();
        defaultProps.setProperty("server.port", "8080");
        defaultProps.setProperty("database.url", "********************************");
        defaultProps.setProperty("log.level", "INFO");
        saveConfig(defaultProps);
    }
}

// 场景2: 日志文件处理
public class LogFileProcessor {

    // 按行读取并处理日志文件
    public List<LogEntry> parseLogFile(String logFilePath) throws IOException {
        List<LogEntry> logEntries = new ArrayList<>();

        try (BufferedReader br = new BufferedReader(new FileReader(logFilePath))) {
            String line;
            while ((line = br.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    LogEntry entry = parseLogLine(line);
                    if (entry != null) {
                        logEntries.add(entry);
                    }
                }
            }
        }
        return logEntries;
    }

    // 写入日志文件
    public void writeLog(String logFilePath, String message) throws IOException {
        try (BufferedWriter bw = new BufferedWriter(
                new FileWriter(logFilePath, true))) { // 追加模式
            bw.write(LocalDateTime.now().toString());
            bw.write(" - ");
            bw.write(message);
            bw.newLine();
        }
    }

    // 清理旧日志文件
    public void cleanOldLogs(String logDir, int daysToKeep) {
        File dir = new File(logDir);
        if (dir.isDirectory()) {
            File[] logFiles = dir.listFiles((file, name) -> name.endsWith(".log"));
            if (logFiles != null) {
                long cutoffTime = System.currentTimeMillis() -
                    (daysToKeep * 24L * 60 * 60 * 1000);

                for (File logFile : logFiles) {
                    if (logFile.lastModified() < cutoffTime) {
                        logFile.delete();
                    }
                }
            }
        }
    }

    private LogEntry parseLogLine(String line) {
        // 解析日志行的逻辑
        return new LogEntry(line);
    }
}

// 场景3: 文件备份和同步
public class FileBackupManager {

    // 备份文件
    public void backupFile(String sourcePath, String backupDir) throws IOException {
        File sourceFile = new File(sourcePath);
        if (!sourceFile.exists()) {
            throw new FileNotFoundException("源文件不存在: " + sourcePath);
        }

        File backupDirectory = new File(backupDir);
        backupDirectory.mkdirs();

        String backupFileName = sourceFile.getName() + "." +
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        File backupFile = new File(backupDirectory, backupFileName);

        copyFile(sourceFile, backupFile);
    }

    // 复制文件
    private void copyFile(File source, File target) throws IOException {
        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(source));
             BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(target))) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
        }
    }

    // 批量处理目录下的文件
    public void processDirectory(String dirPath, FileProcessor processor) throws IOException {
        File dir = new File(dirPath);
        if (!dir.isDirectory()) {
            throw new IllegalArgumentException("不是有效的目录: " + dirPath);
        }

        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile()) {
                    processor.process(file);
                } else if (file.isDirectory()) {
                    // 递归处理子目录
                    processDirectory(file.getAbsolutePath(), processor);
                }
            }
        }
    }

    @FunctionalInterface
    public interface FileProcessor {
        void process(File file) throws IOException;
    }
}

// 场景4: CSV文件读写
public class CsvFileHandler {

    // 读取CSV文件
    public List<Map<String, String>> readCsv(String csvFilePath) throws IOException {
        List<Map<String, String>> records = new ArrayList<>();

        try (BufferedReader br = new BufferedReader(new FileReader(csvFilePath))) {
            String headerLine = br.readLine();
            if (headerLine == null) return records;

            String[] headers = headerLine.split(",");
            String line;

            while ((line = br.readLine()) != null) {
                String[] values = line.split(",");
                Map<String, String> record = new HashMap<>();

                for (int i = 0; i < headers.length && i < values.length; i++) {
                    record.put(headers[i].trim(), values[i].trim());
                }
                records.add(record);
            }
        }
        return records;
    }

    // 写入CSV文件
    public void writeCsv(String csvFilePath, List<Map<String, String>> records) throws IOException {
        if (records.isEmpty()) return;

        Set<String> allKeys = records.stream()
            .flatMap(record -> record.keySet().stream())
            .collect(Collectors.toSet());

        try (BufferedWriter bw = new BufferedWriter(new FileWriter(csvFilePath))) {
            // 写入表头
            bw.write(String.join(",", allKeys));
            bw.newLine();

            // 写入数据行
            for (Map<String, String> record : records) {
                List<String> values = allKeys.stream()
                    .map(key -> record.getOrDefault(key, ""))
                    .collect(Collectors.toList());
                bw.write(String.join(",", values));
                bw.newLine();
            }
        }
    }
}
```

---

## 异常处理

### 异常处理语法

| 语法结构 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `try-catch` | 捕获并处理异常 | `try { code } catch (Exception e) { handle }` | catch可以有多个 |
| `try-catch-finally` | 确保资源释放 | `try { code } catch (Exception e) { handle } finally { cleanup }` | finally总是执行 |
| `try-with-resources` | 自动资源管理 | `try (InputStream is = new FileInputStream("file")) { code }` | 自动调用close() |
| `throws` | 声明抛出异常 | `public void method() throws IOException { code }` | 编译时异常必须声明 |
| `throw` | 手动抛出异常 | `throw new RuntimeException("error message");` | 主动抛出异常 |

### 常见异常类

| 异常类名 | 异常类型 | 触发场景 | 处理建议 |
|---------|---------|---------|---------|
| `NullPointerException` | 运行时异常 | 空指针调用方法 | 检查null值 |
| `ArrayIndexOutOfBoundsException` | 运行时异常 | 数组索引越界 | 检查数组边界 |
| `ClassCastException` | 运行时异常 | 类型转换错误 | 使用instanceof检查 |
| `NumberFormatException` | 运行时异常 | 数字格式转换错误 | 验证输入格式 |
| `IOException` | 编译时异常 | IO操作失败 | 必须try-catch或throws |
| `FileNotFoundException` | 编译时异常 | 文件不存在 | 检查文件路径 |
| `ParseException` | 编译时异常 | 解析错误 | 验证输入格式 |

**异常处理实际应用场景示例：**
```java
// 场景1: 用户输入验证和处理
public class UserInputValidator {

    // 安全的数字转换
    public Integer parseIntegerSafely(String input) {
        if (input == null || input.trim().isEmpty()) {
            return null;
        }

        try {
            return Integer.parseInt(input.trim());
        } catch (NumberFormatException e) {
            System.err.println("无效的数字格式: " + input);
            return null;
        }
    }

    // 验证邮箱格式
    public boolean validateEmail(String email) {
        if (email == null) {
            throw new IllegalArgumentException("邮箱不能为null");
        }

        try {
            return email.matches("^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$");
        } catch (Exception e) {
            System.err.println("邮箱验证失败: " + e.getMessage());
            return false;
        }
    }

    // 安全的数组访问
    public <T> T getArrayElementSafely(T[] array, int index) {
        if (array == null) {
            throw new IllegalArgumentException("数组不能为null");
        }

        try {
            return array[index];
        } catch (ArrayIndexOutOfBoundsException e) {
            System.err.println("数组索引越界: " + index + ", 数组长度: " + array.length);
            return null;
        }
    }
}

// 场景2: 数据库操作异常处理
public class DatabaseManager {

    // 安全的数据库连接
    public Connection getConnection() throws SQLException {
        try {
            String url = "********************************";
            String username = "user";
            String password = "password";
            return DriverManager.getConnection(url, username, password);
        } catch (SQLException e) {
            System.err.println("数据库连接失败: " + e.getMessage());
            throw e; // 重新抛出让调用者处理
        }
    }

    // 执行查询操作
    public List<User> queryUsers() {
        List<User> users = new ArrayList<>();
        String sql = "SELECT id, name, email FROM users";

        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                User user = new User();
                user.setId(rs.getInt("id"));
                user.setName(rs.getString("name"));
                user.setEmail(rs.getString("email"));
                users.add(user);
            }

        } catch (SQLException e) {
            System.err.println("查询用户失败: " + e.getMessage());
            // 记录日志但不抛出异常，返回空列表
        }

        return users;
    }

    // 事务处理
    public boolean transferMoney(int fromAccount, int toAccount, double amount) {
        try (Connection conn = getConnection()) {
            conn.setAutoCommit(false); // 开始事务

            try {
                // 扣款
                deductMoney(conn, fromAccount, amount);
                // 加款
                addMoney(conn, toAccount, amount);

                conn.commit(); // 提交事务
                return true;

            } catch (SQLException e) {
                conn.rollback(); // 回滚事务
                System.err.println("转账失败，已回滚: " + e.getMessage());
                return false;
            }

        } catch (SQLException e) {
            System.err.println("数据库连接失败: " + e.getMessage());
            return false;
        }
    }

    private void deductMoney(Connection conn, int account, double amount) throws SQLException {
        // 扣款逻辑
    }

    private void addMoney(Connection conn, int account, double amount) throws SQLException {
        // 加款逻辑
    }
}

// 场景3: 文件操作异常处理
public class FileOperationManager {

    // 安全读取文件内容
    public String readFileContent(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            StringBuilder content = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                content.append(line).append(System.lineSeparator());
            }

            return content.toString();

        } catch (FileNotFoundException e) {
            System.err.println("文件不存在: " + filePath);
            return null;
        } catch (IOException e) {
            System.err.println("读取文件失败: " + e.getMessage());
            return null;
        }
    }

    // 安全写入文件
    public boolean writeToFile(String filePath, String content) {
        if (filePath == null || content == null) {
            throw new IllegalArgumentException("文件路径和内容不能为null");
        }

        // 确保目录存在
        File file = new File(filePath);
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            writer.write(content);
            return true;

        } catch (IOException e) {
            System.err.println("写入文件失败: " + e.getMessage());
            return false;
        }
    }

    // 批量处理文件，收集所有错误
    public Map<String, String> processFiles(List<String> filePaths) {
        Map<String, String> errors = new HashMap<>();

        for (String filePath : filePaths) {
            try {
                processFile(filePath);
            } catch (Exception e) {
                errors.put(filePath, e.getMessage());
            }
        }

        return errors;
    }

    private void processFile(String filePath) throws IOException {
        // 文件处理逻辑
    }
}

// 场景4: 自定义异常和异常链
public class BusinessException extends Exception {
    private String errorCode;

    public BusinessException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public BusinessException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }
}

public class OrderService {

    public void processOrder(Order order) throws BusinessException {
        try {
            validateOrder(order);
            saveOrder(order);
            sendNotification(order);

        } catch (ValidationException e) {
            throw new BusinessException("订单验证失败", "ORDER_VALIDATION_ERROR", e);
        } catch (DatabaseException e) {
            throw new BusinessException("订单保存失败", "ORDER_SAVE_ERROR", e);
        } catch (NotificationException e) {
            // 通知失败不影响订单处理，只记录日志
            System.err.println("通知发送失败: " + e.getMessage());
        }
    }

    private void validateOrder(Order order) throws ValidationException {
        // 验证逻辑
    }

    private void saveOrder(Order order) throws DatabaseException {
        // 保存逻辑
    }

    private void sendNotification(Order order) throws NotificationException {
        // 通知逻辑
    }
}
```

---

## 线程编程

### Thread类 - 线程创建和控制

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `Thread()` | 创建线程对象 | `Thread t = new Thread();` | 需要重写run方法 |
| `Thread(Runnable target)` | 用Runnable创建线程 | `Thread t = new Thread(runnable);` | 推荐方式 |
| `Thread(String name)` | 创建命名线程 | `Thread t = new Thread("MyThread");` | 便于调试 |
| `start()` | 启动线程 | `t.start();` | 只能调用一次 |
| `run()` | 线程执行体 | `public void run() { code }` | 重写此方法定义任务 |
| `getName()` | 获取线程名称 | `String name = t.getName();` | 默认Thread-0格式 |
| `setName(String name)` | 设置线程名称 | `t.setName("WorkerThread");` | 启动前设置 |
| `currentThread()` | 获取当前线程 | `Thread current = Thread.currentThread();` | 静态方法 |
| `sleep(long millis)` | 线程休眠 | `Thread.sleep(1000);` | 静态方法，毫秒单位 |
| `join()` | 等待线程结束 | `t.join();` | 当前线程等待t结束 |

### Runnable接口 - 任务定义

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `run()` | 定义线程任务 | `public void run() { task code }` | 实现此方法 |

### Callable接口 - 有返回值的任务

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `call()` | 执行任务并返回结果 | `public String call() throws Exception { return result; }` | 可以抛出异常 |

### FutureTask - 未来任务

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `FutureTask(Callable<V> callable)` | 创建未来任务 | `FutureTask<String> task = new FutureTask<>(callable);` | 包装Callable |
| `get()` | 获取任务结果 | `String result = task.get();` | 阻塞等待结果 |
| `get(long timeout, TimeUnit unit)` | 限时获取结果 | `String result = task.get(5, TimeUnit.SECONDS);` | 超时抛异常 |

**线程编程实际应用场景示例：**
```java
// 场景1: 文件下载器（多线程下载）
public class FileDownloader {

    // 单线程下载任务
    public static class DownloadTask implements Runnable {
        private String url;
        private String fileName;
        private DownloadListener listener;

        public DownloadTask(String url, String fileName, DownloadListener listener) {
            this.url = url;
            this.fileName = fileName;
            this.listener = listener;
        }

        @Override
        public void run() {
            try {
                System.out.println("开始下载: " + fileName + " 线程: " + Thread.currentThread().getName());

                // 模拟下载过程
                for (int i = 0; i <= 100; i += 10) {
                    Thread.sleep(200); // 模拟下载时间
                    if (listener != null) {
                        listener.onProgress(fileName, i);
                    }
                }

                if (listener != null) {
                    listener.onComplete(fileName);
                }

            } catch (InterruptedException e) {
                if (listener != null) {
                    listener.onError(fileName, "下载被中断");
                }
            }
        }
    }

    // 批量下载管理
    public void downloadFiles(List<String> urls) {
        List<Thread> downloadThreads = new ArrayList<>();

        for (int i = 0; i < urls.size(); i++) {
            String url = urls.get(i);
            String fileName = "file_" + (i + 1) + ".txt";

            DownloadTask task = new DownloadTask(url, fileName, new DownloadListener() {
                @Override
                public void onProgress(String fileName, int progress) {
                    System.out.println(fileName + " 下载进度: " + progress + "%");
                }

                @Override
                public void onComplete(String fileName) {
                    System.out.println(fileName + " 下载完成！");
                }

                @Override
                public void onError(String fileName, String error) {
                    System.err.println(fileName + " 下载失败: " + error);
                }
            });

            Thread downloadThread = new Thread(task, "Downloader-" + (i + 1));
            downloadThreads.add(downloadThread);
            downloadThread.start();
        }

        // 等待所有下载完成
        for (Thread thread : downloadThreads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                System.err.println("等待下载完成时被中断");
            }
        }

        System.out.println("所有文件下载完成！");
    }

    interface DownloadListener {
        void onProgress(String fileName, int progress);
        void onComplete(String fileName);
        void onError(String fileName, String error);
    }
}

// 场景2: 生产者-消费者模式
public class ProducerConsumerExample {
    private final Queue<String> queue = new LinkedList<>();
    private final int maxSize = 10;
    private final Object lock = new Object();

    // 生产者
    public class Producer implements Runnable {
        @Override
        public void run() {
            for (int i = 1; i <= 20; i++) {
                try {
                    produce("Item-" + i);
                    Thread.sleep(100); // 模拟生产时间
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        private void produce(String item) throws InterruptedException {
            synchronized (lock) {
                while (queue.size() == maxSize) {
                    System.out.println("队列已满，生产者等待...");
                    lock.wait();
                }

                queue.offer(item);
                System.out.println("生产: " + item + " (队列大小: " + queue.size() + ")");
                lock.notifyAll();
            }
        }
    }

    // 消费者
    public class Consumer implements Runnable {
        private String name;

        public Consumer(String name) {
            this.name = name;
        }

        @Override
        public void run() {
            while (true) {
                try {
                    String item = consume();
                    if (item == null) break;
                    Thread.sleep(150); // 模拟消费时间
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        private String consume() throws InterruptedException {
            synchronized (lock) {
                while (queue.isEmpty()) {
                    System.out.println(name + " 队列为空，等待...");
                    lock.wait();
                }

                String item = queue.poll();
                System.out.println(name + " 消费: " + item + " (队列大小: " + queue.size() + ")");
                lock.notifyAll();
                return item;
            }
        }
    }

    public void start() {
        Thread producer = new Thread(new Producer(), "Producer");
        Thread consumer1 = new Thread(new Consumer("Consumer-1"), "Consumer-1");
        Thread consumer2 = new Thread(new Consumer("Consumer-2"), "Consumer-2");

        producer.start();
        consumer1.start();
        consumer2.start();

        try {
            producer.join();
            Thread.sleep(2000); // 等待消费者处理完剩余项目
            consumer1.interrupt();
            consumer2.interrupt();
        } catch (InterruptedException e) {
            System.err.println("主线程被中断");
        }
    }
}

// 场景3: 并行计算任务
public class ParallelCalculator {

    // 计算任务
    public static class CalculationTask implements Callable<Long> {
        private long start;
        private long end;

        public CalculationTask(long start, long end) {
            this.start = start;
            this.end = end;
        }

        @Override
        public Long call() throws Exception {
            long sum = 0;
            for (long i = start; i <= end; i++) {
                sum += i;
                // 模拟复杂计算
                if (i % 1000000 == 0) {
                    Thread.sleep(1);
                }
            }
            System.out.println("线程 " + Thread.currentThread().getName() +
                " 计算范围 [" + start + ", " + end + "] 完成，结果: " + sum);
            return sum;
        }
    }

    // 并行计算1到n的和
    public long calculateSumParallel(long n, int threadCount) {
        List<FutureTask<Long>> tasks = new ArrayList<>();
        List<Thread> threads = new ArrayList<>();

        long rangeSize = n / threadCount;

        // 创建并启动计算任务
        for (int i = 0; i < threadCount; i++) {
            long start = i * rangeSize + 1;
            long end = (i == threadCount - 1) ? n : (i + 1) * rangeSize;

            CalculationTask task = new CalculationTask(start, end);
            FutureTask<Long> futureTask = new FutureTask<>(task);
            tasks.add(futureTask);

            Thread thread = new Thread(futureTask, "Calculator-" + (i + 1));
            threads.add(thread);
            thread.start();
        }

        // 收集结果
        long totalSum = 0;
        for (FutureTask<Long> task : tasks) {
            try {
                totalSum += task.get(); // 等待任务完成并获取结果
            } catch (Exception e) {
                System.err.println("计算任务失败: " + e.getMessage());
            }
        }

        return totalSum;
    }
}

// 场景4: 定时任务执行器
public class SimpleScheduler {
    private volatile boolean running = true;

    // 定时任务
    public static class ScheduledTask implements Runnable {
        private Runnable task;
        private long interval;
        private String name;

        public ScheduledTask(Runnable task, long interval, String name) {
            this.task = task;
            this.interval = interval;
            this.name = name;
        }

        @Override
        public void run() {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    System.out.println("执行定时任务: " + name + " 时间: " +
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                    task.run();
                    Thread.sleep(interval);
                } catch (InterruptedException e) {
                    System.out.println("定时任务 " + name + " 被中断");
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }

    public void startScheduler() {
        // 任务1: 每2秒执行一次
        ScheduledTask task1 = new ScheduledTask(() -> {
            System.out.println("  -> 清理临时文件");
        }, 2000, "清理任务");

        // 任务2: 每3秒执行一次
        ScheduledTask task2 = new ScheduledTask(() -> {
            System.out.println("  -> 检查系统状态");
        }, 3000, "监控任务");

        Thread thread1 = new Thread(task1, "Scheduler-1");
        Thread thread2 = new Thread(task2, "Scheduler-2");

        thread1.start();
        thread2.start();

        // 运行10秒后停止
        try {
            Thread.sleep(10000);
            thread1.interrupt();
            thread2.interrupt();

            thread1.join();
            thread2.join();
            System.out.println("调度器已停止");
        } catch (InterruptedException e) {
            System.err.println("调度器被中断");
        }
    }
}
```

---

## 数学运算

### Math类 - 数学工具类

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `abs(int a)` | 绝对值 | `int result = Math.abs(-5);` | 支持int、long、float、double |
| `max(int a, int b)` | 最大值 | `int max = Math.max(10, 20);` | 支持int、long、float、double |
| `min(int a, int b)` | 最小值 | `int min = Math.min(10, 20);` | 支持int、long、float、double |
| `pow(double a, double b)` | 幂运算 | `double result = Math.pow(2, 3);` | a的b次方 |
| `sqrt(double a)` | 平方根 | `double result = Math.sqrt(16);` | 返回double |
| `ceil(double a)` | 向上取整 | `double result = Math.ceil(3.2);` | 返回4.0 |
| `floor(double a)` | 向下取整 | `double result = Math.floor(3.8);` | 返回3.0 |
| `round(double a)` | 四舍五入 | `long result = Math.round(3.6);` | 返回long |
| `random()` | 随机数 | `double random = Math.random();` | 返回[0.0, 1.0)范围 |

### BigDecimal - 精确小数运算

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `BigDecimal(String val)` | 创建BigDecimal | `BigDecimal bd = new BigDecimal("3.14");` | 推荐用字符串构造 |
| `add(BigDecimal augend)` | 加法运算 | `BigDecimal result = bd1.add(bd2);` | 返回新对象 |
| `subtract(BigDecimal subtrahend)` | 减法运算 | `BigDecimal result = bd1.subtract(bd2);` | 返回新对象 |
| `multiply(BigDecimal multiplicand)` | 乘法运算 | `BigDecimal result = bd1.multiply(bd2);` | 返回新对象 |
| `divide(BigDecimal divisor, int scale, RoundingMode roundingMode)` | 除法运算 | `BigDecimal result = bd1.divide(bd2, 2, RoundingMode.HALF_UP);` | 需指定精度和舍入模式 |
| `compareTo(BigDecimal val)` | 比较大小 | `int result = bd1.compareTo(bd2);` | 返回-1、0、1 |
| `doubleValue()` | 转换为double | `double d = bd.doubleValue();` | 可能丢失精度 |

---

## 时间日期

### LocalDateTime - JDK8时间API

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `now()` | 获取当前时间 | `LocalDateTime now = LocalDateTime.now();` | 静态方法 |
| `of(int year, int month, int dayOfMonth, int hour, int minute, int second)` | 创建指定时间 | `LocalDateTime dt = LocalDateTime.of(2023, 12, 25, 10, 30, 0);` | 静态方法 |
| `getYear()` | 获取年份 | `int year = dt.getYear();` | 返回int |
| `getMonthValue()` | 获取月份 | `int month = dt.getMonthValue();` | 返回1-12 |
| `getDayOfMonth()` | 获取日期 | `int day = dt.getDayOfMonth();` | 返回1-31 |
| `getHour()` | 获取小时 | `int hour = dt.getHour();` | 返回0-23 |
| `getMinute()` | 获取分钟 | `int minute = dt.getMinute();` | 返回0-59 |
| `getSecond()` | 获取秒 | `int second = dt.getSecond();` | 返回0-59 |
| `plusYears(long years)` | 增加年份 | `LocalDateTime future = dt.plusYears(1);` | 返回新对象 |
| `plusMonths(long months)` | 增加月份 | `LocalDateTime future = dt.plusMonths(6);` | 返回新对象 |
| `plusDays(long days)` | 增加天数 | `LocalDateTime future = dt.plusDays(30);` | 返回新对象 |
| `minusYears(long years)` | 减少年份 | `LocalDateTime past = dt.minusYears(1);` | 返回新对象 |

### DateTimeFormatter - 时间格式化

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `ofPattern(String pattern)` | 创建格式化器 | `DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");` | 静态方法 |
| `format(TemporalAccessor temporal)` | 格式化时间 | `String formatted = formatter.format(dateTime);` | 时间转字符串 |
| `parse(CharSequence text)` | 解析时间字符串 | `LocalDateTime parsed = LocalDateTime.parse("2023-12-25 10:30:00", formatter);` | 字符串转时间 |

### 常用时间格式模式

| 模式字符 | 含义 | 示例 |
|---------|------|------|
| `yyyy` | 4位年份 | 2023 |
| `MM` | 2位月份 | 01-12 |
| `dd` | 2位日期 | 01-31 |
| `HH` | 24小时制小时 | 00-23 |
| `mm` | 分钟 | 00-59 |
| `ss` | 秒 | 00-59 |

---

## 正则表达式

### Pattern类 - 正则模式

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `compile(String regex)` | 编译正则表达式 | `Pattern pattern = Pattern.compile("\\d+");` | 静态方法 |
| `matcher(CharSequence input)` | 创建匹配器 | `Matcher matcher = pattern.matcher("abc123");` | 返回Matcher对象 |
| `matches(String regex, CharSequence input)` | 静态匹配方法 | `boolean result = Pattern.matches("\\d+", "123");` | 整个字符串匹配 |

### Matcher类 - 匹配器

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `matches()` | 整个字符串匹配 | `boolean result = matcher.matches();` | 全部匹配才返回true |
| `find()` | 查找下一个匹配 | `boolean found = matcher.find();` | 部分匹配即可 |
| `group()` | 获取匹配的内容 | `String matched = matcher.group();` | 需要先调用find() |
| `replaceAll(String replacement)` | 替换所有匹配 | `String result = matcher.replaceAll("XXX");` | 全局替换 |
| `replaceFirst(String replacement)` | 替换第一个匹配 | `String result = matcher.replaceFirst("XXX");` | 只替换第一个 |

### 常用正则表达式

| 模式 | 含义 | 示例 |
|------|------|------|
| `\\d` | 数字字符 | 匹配0-9 |
| `\\w` | 单词字符 | 匹配字母、数字、下划线 |
| `\\s` | 空白字符 | 匹配空格、制表符、换行符 |
| `[a-zA-Z]` | 字母字符 | 匹配大小写字母 |
| `[0-9]` | 数字字符 | 等同于\\d |
| `+` | 一个或多个 | `\\d+`匹配一个或多个数字 |
| `*` | 零个或多个 | `\\w*`匹配零个或多个单词字符 |
| `?` | 零个或一个 | `\\d?`匹配零个或一个数字 |
| `{n}` | 恰好n个 | `\\d{3}`匹配恰好3个数字 |
| `{n,m}` | n到m个 | `\\d{2,4}`匹配2到4个数字 |

---

## Lambda表达式

### Lambda语法

| 语法形式 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `() -> expression` | 无参数Lambda | `() -> System.out.println("Hello")` | 无参数时用空括号 |
| `param -> expression` | 单参数Lambda | `x -> x * 2` | 单参数可省略括号 |
| `(param1, param2) -> expression` | 多参数Lambda | `(a, b) -> a + b` | 多参数必须用括号 |
| `param -> { statements; }` | 代码块Lambda | `x -> { System.out.println(x); return x * 2; }` | 多语句用大括号 |

### 方法引用

| 引用类型 | 语法 | 使用示例 | 等价Lambda |
|---------|------|---------|-----------|
| 静态方法引用 | `ClassName::staticMethod` | `Integer::parseInt` | `s -> Integer.parseInt(s)` |
| 实例方法引用 | `instance::instanceMethod` | `System.out::println` | `s -> System.out.println(s)` |
| 类型方法引用 | `ClassName::instanceMethod` | `String::length` | `s -> s.length()` |
| 构造器引用 | `ClassName::new` | `ArrayList::new` | `() -> new ArrayList()` |

### 常用函数式接口

| 接口名 | 方法签名 | 功能描述 | 使用示例 |
|-------|---------|---------|---------|
| `Predicate<T>` | `boolean test(T t)` | 判断条件 | `Predicate<String> isEmpty = String::isEmpty` |
| `Function<T, R>` | `R apply(T t)` | 函数转换 | `Function<String, Integer> length = String::length` |
| `Consumer<T>` | `void accept(T t)` | 消费操作 | `Consumer<String> print = System.out::println` |
| `Supplier<T>` | `T get()` | 供应操作 | `Supplier<String> supplier = () -> "Hello"` |
| `BinaryOperator<T>` | `T apply(T t1, T t2)` | 二元操作 | `BinaryOperator<Integer> add = (a, b) -> a + b` |

---

## 工具类方法

### Arrays类 - 数组工具类

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `toString(int[] a)` | 数组转字符串 | `String str = Arrays.toString(array);` | 返回[1, 2, 3]格式 |
| `sort(int[] a)` | 数组排序 | `Arrays.sort(array);` | 直接修改原数组 |
| `binarySearch(int[] a, int key)` | 二分查找 | `int index = Arrays.binarySearch(array, 5);` | 数组必须已排序 |
| `copyOf(int[] original, int newLength)` | 复制数组 | `int[] copy = Arrays.copyOf(array, 10);` | 指定新长度 |
| `fill(int[] a, int val)` | 填充数组 | `Arrays.fill(array, 0);` | 用指定值填充 |
| `equals(int[] a, int[] a2)` | 比较数组 | `boolean same = Arrays.equals(arr1, arr2);` | 比较内容是否相同 |
| `asList(T... a)` | 数组转List | `List<String> list = Arrays.asList("a", "b", "c");` | 返回固定大小的List |

### Objects类 - 对象工具类

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `equals(Object a, Object b)` | 安全比较 | `boolean same = Objects.equals(obj1, obj2);` | 处理null值 |
| `isNull(Object obj)` | 判断是否为null | `boolean isNull = Objects.isNull(obj);` | 等同于obj == null |
| `nonNull(Object obj)` | 判断是否非null | `boolean nonNull = Objects.nonNull(obj);` | 等同于obj != null |
| `requireNonNull(T obj)` | 要求非null | `String str = Objects.requireNonNull(input);` | null时抛异常 |
| `toString(Object o)` | 安全转字符串 | `String str = Objects.toString(obj);` | null返回"null" |

### Properties类 - 属性文件处理

| 方法签名 | 功能描述 | 使用示例 | 注意事项 |
|---------|---------|---------|---------|
| `load(InputStream inStream)` | 加载属性文件 | `props.load(new FileInputStream("config.properties"));` | 从流加载 |
| `getProperty(String key)` | 获取属性值 | `String value = props.getProperty("username");` | 键不存在返回null |
| `getProperty(String key, String defaultValue)` | 获取属性值(带默认值) | `String value = props.getProperty("port", "8080");` | 键不存在返回默认值 |
| `setProperty(String key, String value)` | 设置属性值 | `props.setProperty("timeout", "30");` | 添加或修改属性 |
| `store(OutputStream out, String comments)` | 保存属性文件 | `props.store(new FileOutputStream("config.properties"), "Configuration");` | 保存到流 |
| `stringPropertyNames()` | 获取所有属性名 | `Set<String> keys = props.stringPropertyNames();` | 返回Set集合 |

---

## 💡 最佳实践建议

### 1. 字符串处理
- 大量字符串拼接使用`StringBuilder`而不是`+`操作符
- 需要格式化输出时优先使用`StringJoiner`
- 正则表达式复杂时先编译`Pattern`再重复使用

### 2. 集合使用
- 优先使用接口类型声明变量：`List<String> list = new ArrayList<>();`
- 已知集合大小时指定初始容量：`new ArrayList<>(100)`
- 使用`Collections.unmodifiableList()`创建不可修改集合

### 3. Stream流操作
- 优先使用Stream API进行集合操作，代码更简洁
- 注意Stream是一次性的，不能重复使用
- 大数据量时考虑使用并行流：`parallelStream()`

### 4. 异常处理
- 优先使用`try-with-resources`自动管理资源
- 不要忽略异常，至少要记录日志
- 自定义异常继承合适的异常类

### 5. 线程编程
- 优先使用`Runnable`接口而不是继承`Thread`类
- 使用线程池管理线程，避免频繁创建销毁
- 注意线程安全，使用同步机制保护共享资源

### 6. 时间处理
- JDK8+项目使用新时间API（`LocalDateTime`等）
- 避免使用过时的`Date`和`Calendar`类
- 时间格式化使用`DateTimeFormatter`而不是`SimpleDateFormat`

---

## 📚 相关学习资源

- [Oracle Java官方文档](https://docs.oracle.com/javase/)
- [Java API文档](https://docs.oracle.com/javase/8/docs/api/)
- [Java编程思想](https://book.douban.com/subject/2130190/)
- [Effective Java](https://book.douban.com/subject/3360807/)

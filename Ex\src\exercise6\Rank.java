package exercise6;

/**
 * 练习6：扑克牌点数枚举
 * 
 * 要求：
 * 1. 定义13种点数：A、2-10、J、Q、K
 * 2. 每种点数有显示名称和数值
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public enum Rank {
    
    // TODO: 定义13种点数
    // 提示：ACE("A", 1), T<PERSON><PERSON>("2", 2), ..., JACK("J", 11), QUEEN("Q", 12), KING("K", 13);
    THREE("3", 3), FOUR("4", 4), FIVE("5", 5), SIX("6", 6),
    SEVEN("7", 7), EIGHT("8", 8), NINE("9", 9), T<PERSON>("10", 10),
    JACK("J", 11), QUEEN("Q", 12), KING("K", 13),
    ACE("A", 14), TWO("2", 15),
    SMALL_JOKER("小王", 16), BIG_JOKER("大王", 17);


    // TODO: 定义属性

    // ✅ 正确写法
    private final String symbol;  // 显示符号，如"3", "J", "小王"
    private final int value;      // 点数值，如3, 11, 16



    // TODO: 构造方法
    Rank(String symbol, int value){
        this.symbol = symbol;
        this.value = value;
    }


    // TODO: getter方法

    public String getSymbol() {
        return symbol;
    }

    public int getValue() {
        return value;
    }


    // TODO: toString方法

    @Override
    public String toString() {
        return "Rank{" +
                "symbol='" + symbol + '\'' +
                ", value=" + value +
                '}';
    }
}

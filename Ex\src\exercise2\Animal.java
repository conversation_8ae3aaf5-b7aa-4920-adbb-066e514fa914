package exercise2;

/**
 * 练习2：动物园管理 - 动物基类
 * 
 * 要求实现：
 * 1. 抽象类Animal，包含name和age属性
 * 2. 抽象方法makeSound()
 * 3. 普通方法eat()和toString()
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public abstract class Animal {
    
    // TODO: 定义属性
    private String name;
    private int age;
    
    // TODO: 构造方法
    public Animal() {
    }

    public Animal(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    
    // TODO: 抽象方法 - 发出声音
    public abstract void makeSound();
    
    // TODO: 普通方法 - 吃东西
    public void eat() {
        // 你的实现
        System.out.println("eating");
    }
    
    // TODO: getter方法
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    // TODO: toString方法


    @Override
    public String toString() {
        return "Animal{" +
                "name='" + name + '\'' +
                ", age=" + age +
                '}';
    }
}

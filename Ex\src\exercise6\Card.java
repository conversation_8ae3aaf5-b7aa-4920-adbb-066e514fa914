package exercise6;

import java.util.Objects;

/**
 * 练习6：扑克牌类
 * 
 * 要求：
 * 1. 包含花色和点数属性
 * 2. 实现toString()和getValue()方法
 * 3. 实现equals()和hashCode()方法
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class Card {
    
    // TODO: 定义属性
    Rank rank;
    Suit suit;


    // TODO: 构造方法
    public Card(Rank rank, Suit suit) {
        this.rank = rank;
        this.suit = suit;
    }

    // TODO: getter方法


    public Rank getRank() {
        return rank;
    }

    public Suit getSuit() {
        return suit;
    }

    // TODO: 获取牌的数值（A=1，J=11，Q=12，K=13）
    public int getValue() {
        // 你的实现
        return rank.getValue();
    }

    // TODO: equals方法
    // TODO: hashCode方法

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        Card card = (Card) o;
        return rank == card.rank && suit == card.suit;
    }

    @Override
    public int hashCode() {
        return Objects.hash(rank, suit);
    }


    // TODO: toString方法

    @Override
    public String toString() {
        return "Card{" +
                "rank=" + rank +
                ", suit=" + suit +
                '}';
    }
}

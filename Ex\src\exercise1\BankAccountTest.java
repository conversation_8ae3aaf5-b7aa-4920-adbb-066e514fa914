package exercise1;

/**
 * 银行账户类测试程序
 * 
 * 测试要求：
 * 1. 创建2个账户对象
 * 2. 进行存款、取款操作
 * 3. 输出最终账户信息
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class BankAccountTest {
    
    public static void main(String[] args) {
        System.out.println("=== 银行账户管理系统测试 ===\n");
        
        // TODO: 创建第一个账户
        // 提示：BankAccount account1 = new BankAccount(...);
        BankAccount account1 = new BankAccount("102",100,"牛");
        
        // TODO: 创建第二个账户
        BankAccount account2 = new BankAccount();
        
        // TODO: 测试存款功能
        System.out.println("--- 存款测试 ---");
        // 提示：account1.deposit(1000);
        account1.deposit(500);
        
        // TODO: 测试取款功能
        System.out.println("\n--- 取款测试 ---");
        // 提示：测试正常取款和余额不足的情况
       if(account1.withdraw(500)){
           System.out.println("取款成功");
       }else{
           System.out.println("取款失败");
       }
        
        // TODO: 测试余额查询
        System.out.println("\n--- 余额查询 ---");
        System.out.println(account1.getBalance());


        // TODO: 输出最终账户信息
        System.out.println("\n--- 最终账户信息 ---");
        account1.toString();
        
        
        System.out.println("\n=== 测试完成 ===");
    }
}

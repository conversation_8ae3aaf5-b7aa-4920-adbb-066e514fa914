package src;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Java综合练习题 - 随机混合练习
 * 涵盖：OOP、泛型、集合、异常处理、内部类、枚举等知识点
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class RandomMixedExercises {
    
    public static void main(String[] args) {
        System.out.println("=== Java综合练习题开始 ===\n");
        
        // 练习1：学生管理系统
        testStudentManagement();
        
        // 练习2：图书馆管理
        testLibraryManagement();
        
        // 练习3：员工薪资计算
        testEmployeeSalary();
        
        // 练习4：商品订单处理
        testOrderProcessing();
        
        System.out.println("\n=== 所有练习完成 ===");
    }
    
    // ==================== 练习1：学生管理系统 ====================
    static void testStudentManagement() {
        System.out.println("【练习1：学生管理系统】");
        
        StudentManager manager = new StudentManager();
        
        // 添加学生
        manager.addStudent(new Student("张三", 20, Grade.SOPHOMORE, 85.5));
        manager.addStudent(new Student("李四", 19, Grade.FRESHMAN, 92.0));
        manager.addStudent(new Student("王五", 21, Grade.JUNIOR, 78.5));
        manager.addStudent(new Student("赵六", 22, Grade.SENIOR, 88.0));
        
        // 显示所有学生
        manager.displayAllStudents();
        
        // 按成绩排序
        manager.sortByScore();
        System.out.println("\n按成绩排序后：");
        manager.displayAllStudents();
        
        // 查找优秀学生
        manager.findExcellentStudents().forEach(System.out::println);
        
        System.out.println();
    }
    
    // ==================== 练习2：图书馆管理 ====================
    static void testLibraryManagement() {
        System.out.println("【练习2：图书馆管理】");
        
        Library library = new Library();
        
        // 添加图书
        library.addBook(new Book("Java编程思想", "Bruce Eckel", BookType.PROGRAMMING));
        library.addBook(new Book("红楼梦", "曹雪芹", BookType.LITERATURE));
        library.addBook(new Book("数据结构", "严蔚敏", BookType.TEXTBOOK));
        
        // 显示所有图书
        library.displayBooks();
        
        // 借书和还书
        try {
            library.borrowBook("Java编程思想");
            library.borrowBook("红楼梦");
            library.returnBook("Java编程思想");
        } catch (LibraryException e) {
            System.out.println("图书馆操作异常：" + e.getMessage());
        }
        
        System.out.println();
    }
    
    // ==================== 练习3：员工薪资计算 ====================
    static void testEmployeeSalary() {
        System.out.println("【练习3：员工薪资计算】");
        
        List<Employee> employees = Arrays.asList(
            new FullTimeEmployee("张经理", 10000),
            new PartTimeEmployee("李助理", 50, 160),
            new FullTimeEmployee("王主管", 8000),
            new PartTimeEmployee("赵实习", 30, 120)
        );
        
        // 计算总薪资
        double totalSalary = employees.stream()
            .mapToDouble(Employee::calculateSalary)
            .sum();
        
        System.out.println("员工薪资明细：");
        employees.forEach(emp -> 
            System.out.printf("%s: %.2f元\n", emp.getName(), emp.calculateSalary()));
        
        System.out.printf("总薪资支出: %.2f元\n\n", totalSalary);
    }
    
    // ==================== 练习4：商品订单处理 ====================
    static void testOrderProcessing() {
        System.out.println("【练习4：商品订单处理】");
        
        OrderProcessor processor = new OrderProcessor();
        
        // 创建订单
        Order order = new Order("ORD001");
        order.addItem(new OrderItem("笔记本电脑", 5999.0, 1));
        order.addItem(new OrderItem("鼠标", 99.0, 2));
        order.addItem(new OrderItem("键盘", 299.0, 1));
        
        // 处理订单
        processor.processOrder(order);
        
        System.out.println();
    }
}

// ==================== 枚举定义 ====================
enum Grade {
    FRESHMAN("大一"), SOPHOMORE("大二"), JUNIOR("大三"), SENIOR("大四");
    
    private String description;
    
    Grade(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}

enum BookType {
    PROGRAMMING, LITERATURE, TEXTBOOK, SCIENCE, HISTORY
}

// ==================== 学生管理相关类 ====================
class Student {
    private String name;
    private int age;
    private Grade grade;
    private double score;
    
    public Student(String name, int age, Grade grade, double score) {
        this.name = name;
        this.age = age;
        this.grade = grade;
        this.score = score;
    }
    
    // Getters
    public String getName() { return name; }
    public int getAge() { return age; }
    public Grade getGrade() { return grade; }
    public double getScore() { return score; }
    
    @Override
    public String toString() {
        return String.format("Student{name='%s', age=%d, grade=%s, score=%.1f}", 
                           name, age, grade.getDescription(), score);
    }
}

class StudentManager {
    private List<Student> students = new ArrayList<>();
    
    public void addStudent(Student student) {
        students.add(student);
    }
    
    public void displayAllStudents() {
        students.forEach(System.out::println);
    }
    
    public void sortByScore() {
        students.sort((s1, s2) -> Double.compare(s2.getScore(), s1.getScore()));
    }
    
    public List<Student> findExcellentStudents() {
        System.out.println("\n优秀学生（成绩>=90）：");
        return students.stream()
                      .filter(s -> s.getScore() >= 90)
                      .collect(Collectors.toList());
    }
}

// ==================== 图书馆管理相关类 ====================
class Book {
    private String title;
    private String author;
    private BookType type;
    private boolean borrowed = false;
    
    public Book(String title, String author, BookType type) {
        this.title = title;
        this.author = author;
        this.type = type;
    }
    
    // Getters and Setters
    public String getTitle() { return title; }
    public String getAuthor() { return author; }
    public BookType getType() { return type; }
    public boolean isBorrowed() { return borrowed; }
    public void setBorrowed(boolean borrowed) { this.borrowed = borrowed; }
    
    @Override
    public String toString() {
        return String.format("《%s》- %s [%s] %s", 
                           title, author, type, borrowed ? "(已借出)" : "(可借阅)");
    }
}

class LibraryException extends Exception {
    public LibraryException(String message) {
        super(message);
    }
}

class Library {
    private Map<String, Book> books = new HashMap<>();

    public void addBook(Book book) {
        books.put(book.getTitle(), book);
    }

    public void displayBooks() {
        System.out.println("图书馆藏书：");
        books.values().forEach(System.out::println);
        System.out.println();
    }

    public void borrowBook(String title) throws LibraryException {
        Book book = books.get(title);
        if (book == null) {
            throw new LibraryException("图书不存在：" + title);
        }
        if (book.isBorrowed()) {
            throw new LibraryException("图书已被借出：" + title);
        }
        book.setBorrowed(true);
        System.out.println("成功借阅：" + title);
    }

    public void returnBook(String title) throws LibraryException {
        Book book = books.get(title);
        if (book == null) {
            throw new LibraryException("图书不存在：" + title);
        }
        if (!book.isBorrowed()) {
            throw new LibraryException("图书未被借出：" + title);
        }
        book.setBorrowed(false);
        System.out.println("成功归还：" + title);
    }
}

// ==================== 员工薪资相关类 ====================
abstract class Employee {
    protected String name;

    public Employee(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    // 抽象方法：计算薪资
    public abstract double calculateSalary();
}

class FullTimeEmployee extends Employee {
    private double monthlySalary;

    public FullTimeEmployee(String name, double monthlySalary) {
        super(name);
        this.monthlySalary = monthlySalary;
    }

    @Override
    public double calculateSalary() {
        return monthlySalary;
    }
}

class PartTimeEmployee extends Employee {
    private double hourlyRate;
    private int hoursWorked;

    public PartTimeEmployee(String name, double hourlyRate, int hoursWorked) {
        super(name);
        this.hourlyRate = hourlyRate;
        this.hoursWorked = hoursWorked;
    }

    @Override
    public double calculateSalary() {
        return hourlyRate * hoursWorked;
    }
}

// ==================== 订单处理相关类 ====================
class OrderItem {
    private String productName;
    private double price;
    private int quantity;

    public OrderItem(String productName, double price, int quantity) {
        this.productName = productName;
        this.price = price;
        this.quantity = quantity;
    }

    public double getTotalPrice() {
        return price * quantity;
    }

    // Getters
    public String getProductName() { return productName; }
    public double getPrice() { return price; }
    public int getQuantity() { return quantity; }

    @Override
    public String toString() {
        return String.format("%s x%d = %.2f元", productName, quantity, getTotalPrice());
    }
}

class Order {
    private String orderId;
    private List<OrderItem> items = new ArrayList<>();
    private double discount = 0.0;

    public Order(String orderId) {
        this.orderId = orderId;
    }

    public void addItem(OrderItem item) {
        items.add(item);
    }

    public double calculateTotal() {
        double subtotal = items.stream()
                              .mapToDouble(OrderItem::getTotalPrice)
                              .sum();
        return subtotal * (1 - discount);
    }

    public void applyDiscount(double discount) {
        this.discount = Math.max(0, Math.min(1, discount)); // 限制在0-1之间
    }

    // Getters
    public String getOrderId() { return orderId; }
    public List<OrderItem> getItems() { return items; }
    public double getDiscount() { return discount; }
}

class OrderProcessor {

    // 使用匿名内部类实现订单验证器
    private OrderValidator validator = new OrderValidator() {
        @Override
        public boolean validate(Order order) {
            return order.getItems().size() > 0 && order.calculateTotal() > 0;
        }
    };

    public void processOrder(Order order) {
        System.out.println("处理订单：" + order.getOrderId());

        if (!validator.validate(order)) {
            System.out.println("订单验证失败！");
            return;
        }

        // 显示订单详情
        System.out.println("订单明细：");
        order.getItems().forEach(item -> System.out.println("  " + item));

        // 应用折扣（满500减50）
        if (order.calculateTotal() >= 500) {
            order.applyDiscount(0.1); // 9折
            System.out.println("应用折扣：满500减10%");
        }

        System.out.printf("订单总额：%.2f元\n", order.calculateTotal());
        System.out.println("订单处理完成！");
    }

    // 函数式接口
    @FunctionalInterface
    interface OrderValidator {
        boolean validate(Order order);
    }
}

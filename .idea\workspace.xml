<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a997ab81-a887-4253-92ab-70ab4cf716ff" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="30aJI0AgbwL1K1VEj2ZAXp5V6CS" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Downloaded.Files.Path.Enabled&quot;: &quot;false&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Repository.Attach.Annotations&quot;: &quot;false&quot;,
    &quot;Repository.Attach.JavaDocs&quot;: &quot;false&quot;,
    &quot;Repository.Attach.Sources&quot;: &quot;false&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Code/ST-Java/Java-01/JavaSEProMax/day12-thread-create/src/d6_thread_synchronized_lock&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;onboarding.tips.debug.path&quot;: &quot;D:/Code/ST-Java/Java-01/JavaSEProMax/day11-special-file-log-code/src/Main.java&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;File.Encoding&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.BankAccountTest.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.BufferedInputStreamDemo1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.CalculatorTest.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.CardGameTest.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.CharSetDemo1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.Collection.Ex1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.CopyTest5.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.Dom4JTest1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.Ex1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.Ex2.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.Ex3.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FileDemo1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FileDemo3.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FileDemo4.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FileInputStreamDemo1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FileInputStreamDemo2.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FileInputStreamDemo3.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FileOutputStreamDemo4.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FileReaderDemo1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FileReaderDemo2.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FileSearchTest5.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FileWriteDemo3.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FinallyDemo1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FinallyDemo2.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.MapDemo1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.MapDemo2.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.MapDemo3.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.MapTest.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.PropertiesDemo1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.PropertiesDemo2.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.PropertiesDemo3.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.StreamExercise1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.StreamExercise2.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.StreamTest1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.StreamTest2.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.StreamTest3.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.StreamTest4.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.Test.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.ThreadDemo1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.ThreadDemo2.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.ThreadDemo2_2.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.ThreadDemo3.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.ThreadSafeTest.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.d2_thread_api.ThreadDemo1 (1).executor&quot;: &quot;Run&quot;,
    &quot;应用程序.d2_thread_api.ThreadDemo1.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.d2_thread_api.ThreadDemo2.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.d2_thread_api.ThreadDemo3.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.d4_thread_synchronized_code.ThreadSafeTest.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.d5__thread_synchronized_method.ThreadSafeTest.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.d5_map_reavesal.MapDemo2.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.d6_thread_synchronized_lock.ThreadSafeTest.executor&quot;: &quot;Run&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Code\ST-Java\Java-01\JavaSEProMax\day12-thread-create\src\d6_thread_synchronized_lock" />
      <recent name="D:\Code\ST-Java\Java-01\JavaSEProMax\day12-thread-create\src\d5__thread_synchronized_method" />
      <recent name="D:\Code\ST-Java\Java-01\JavaSEProMax\day12-thread-create\src\d4_thread_synchronized_code" />
      <recent name="D:\Code\ST-Java\Java-01\JavaSEProMax\day12-thread-create\src\d2_thread_api" />
      <recent name="D:\Code\ST-Java\Java-01\JavaSEProMax\day11-special-file-log-code\lib" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Code\ST-Java\Java-01\JavaSEProMax\day11-special-file-log-code\src" />
      <recent name="D:\Code\ST-Java\Java-01\JavaSEProMax\day09-io-code\src" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="d2_finally" />
    </key>
  </component>
  <component name="RunManager" selected="应用程序.CalculatorTest">
    <configuration name="BankAccountTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="exercise1.BankAccountTest" />
      <module name="Ex" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="exercise1.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CalculatorTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="exercise4.CalculatorTest" />
      <module name="Ex" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="exercise4.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CardGameTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="exercise6.CardGameTest" />
      <module name="Ex" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="exercise6.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="StreamExercise1" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="StreamExercise1" />
      <module name="Ex" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="StreamExercise2" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="StreamExercise2" />
      <module name="Ex" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.CalculatorTest" />
        <item itemvalue="应用程序.CardGameTest" />
        <item itemvalue="应用程序.BankAccountTest" />
        <item itemvalue="应用程序.StreamExercise2" />
        <item itemvalue="应用程序.StreamExercise1" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="a997ab81-a887-4253-92ab-70ab4cf716ff" name="更改" comment="" />
      <created>*************</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>*************</updated>
      <workItem from="*************" duration="8000" />
      <workItem from="*************" duration="176000" />
      <workItem from="1753859939143" duration="550000" />
      <workItem from="1753860518897" duration="382000" />
      <workItem from="1753860953041" duration="287000" />
      <workItem from="1753861259119" duration="367000" />
      <workItem from="1753864694766" duration="2437000" />
      <workItem from="1753871880877" duration="5366000" />
      <workItem from="1753884613011" duration="656000" />
      <workItem from="1753885362734" duration="82000" />
      <workItem from="1753885460514" duration="3478000" />
      <workItem from="1753923953602" duration="6351000" />
      <workItem from="1753933635514" duration="2450000" />
      <workItem from="1753941341130" duration="5685000" />
      <workItem from="1753947711163" duration="902000" />
      <workItem from="1753949367296" duration="4640000" />
      <workItem from="1753960007716" duration="2376000" />
      <workItem from="1753963052437" duration="1460000" />
      <workItem from="1753972121535" duration="1182000" />
      <workItem from="1754010098390" duration="4130000" />
      <workItem from="1754028767475" duration="1570000" />
      <workItem from="1754030359425" duration="131000" />
      <workItem from="1754030688216" duration="10352000" />
      <workItem from="1754043648579" duration="257000" />
      <workItem from="1754097375782" duration="17293000" />
      <workItem from="1754144121420" duration="138000" />
      <workItem from="1754144281495" duration="80000" />
      <workItem from="1754144419862" duration="1693000" />
      <workItem from="1754146160654" duration="2536000" />
      <workItem from="1754149816295" duration="3972000" />
      <workItem from="1754187034254" duration="8162000" />
      <workItem from="1754212403490" duration="13000" />
      <workItem from="1754212640872" duration="8794000" />
      <workItem from="1754268910628" duration="96000" />
      <workItem from="1754269036726" duration="472000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>
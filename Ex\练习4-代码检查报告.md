# 练习4：异常处理 - 代码检查报告 📋

## 🎯 总体评价

**完成度**：⚠️ 40% - 基本框架已搭建，但存在多个严重问题

**代码质量**：🔴 需要大幅改进 - 异常处理逻辑错误，方法实现不完整

## 🚨 严重问题需要立即修复

### 1. Calculator类的致命问题

#### 问题1：divide方法异常声明错误
**当前代码**：
```java
public double divide(double a, double b) throws Exception {
    return a/b;  // 没有检查除零情况
}
```

**问题**：
- 声明抛出`Exception`而不是`DivideByZeroException`
- 没有检查除零情况
- 直接返回`a/b`会返回`Infinity`而不是抛出异常

**正确实现**：
```java
public double divide(double a, double b) throws DivideByZeroException {
    if (b == 0) {
        throw new DivideByZeroException("除数不能为零：" + a + " / " + b);
    }
    return a / b;
}
```

#### 问题2：calculate方法逻辑完全错误
**当前代码**：
```java
public double calculate(double a, double b, String operator) 
        throws DivideByZeroException, InvalidOperatorException {
    switch (operator) {
        case "/":
            try {
                divide(a,b);  // 调用了但没有返回结果
            } catch (Exception e) {
                throw new RuntimeException(e);  // 错误的异常转换
            }
            break;
        case "*":
            multiply(a,b);  // 调用了但没有返回结果
            break;
        case "-":
            subtract(a,b);  // 调用了但没有返回结果
            break;
        case "+":
            add(a,b);  // 调用了但没有返回结果
    }
    return 0.0;  // 总是返回0.0
}
```

**问题分析**：
- 所有case都没有`return`语句
- 调用了运算方法但没有返回结果
- 异常处理错误：将`DivideByZeroException`转换为`RuntimeException`
- 没有处理无效操作符的情况
- 最后总是返回`0.0`

**正确实现**：
```java
public double calculate(double a, double b, String operator) 
        throws DivideByZeroException, InvalidOperatorException {
    
    if (operator == null) {
        throw new InvalidOperatorException("操作符不能为空");
    }
    
    switch (operator) {
        case "+":
            return add(a, b);
        case "-":
            return subtract(a, b);
        case "*":
            return multiply(a, b);
        case "/":
            return divide(a, b);  // 会自动抛出DivideByZeroException
        default:
            throw new InvalidOperatorException("不支持的操作符：" + operator);
    }
}
```

### 2. 自定义异常类问题

#### 问题：无参构造方法没有提供默认消息
**当前代码**：
```java
public DivideByZeroException() {}  // 空构造方法
```

**建议改进**：
```java
public DivideByZeroException() {
    super("除数不能为零");  // 提供默认错误消息
}
```

### 3. 测试类问题

#### 问题1：方法调用没有输出结果
**当前代码**：
```java
calculator.calculate(10,2,"*");  // 调用了但没有输出结果
calculator.calculate(10,2,"+");
calculator.calculate(10,2,"-");
calculator.calculate(10,2,"/");
```

**问题**：调用了方法但没有打印结果，看不到计算结果

**正确实现**：
```java
try {
    double result1 = calculator.calculate(10, 2, "*");
    System.out.println("10 * 2 = " + result1);
    
    double result2 = calculator.calculate(10, 2, "+");
    System.out.println("10 + 2 = " + result2);
    
    double result3 = calculator.calculate(10, 2, "-");
    System.out.println("10 - 2 = " + result3);
    
    double result4 = calculator.calculate(10, 2, "/");
    System.out.println("10 / 2 = " + result4);
    
} catch (DivideByZeroException | InvalidOperatorException e) {
    System.err.println("计算错误：" + e.getMessage());
}
```

#### 问题2：除零测试错误
**当前代码**：
```java
calculator.calculate(10,2,"0");  // "0"不是除零，是无效操作符
```

**应该改为**：
```java
try {
    double result = calculator.calculate(10, 0, "/");  // 真正的除零
    System.out.println("结果：" + result);
} catch (DivideByZeroException e) {
    System.err.println("除零错误：" + e.getMessage());
}
```

#### 问题3：缺少异常处理
**当前代码**：
```java
public static void main(String[] args) throws Exception {
    // 直接抛出异常而不是处理
}
```

**应该改为**：
```java
public static void main(String[] args) {
    // 在方法内部使用try-catch处理异常
}
```

## ✅ 做得对的地方

### 1. 基本结构正确
- ✅ 自定义异常类正确继承了Exception
- ✅ 提供了带消息的构造方法
- ✅ Calculator类的方法签名基本正确

### 2. 基本运算实现正确
- ✅ add、subtract、multiply方法实现正确
- ✅ 使用了合适的返回类型

## 🚀 完整的修复版本

### 修复后的Calculator.java
```java
public class Calculator {
    
    public double add(double a, double b) {
        return a + b;
    }
    
    public double subtract(double a, double b) {
        return a - b;
    }
    
    public double multiply(double a, double b) {
        return a * b;
    }
    
    public double divide(double a, double b) throws DivideByZeroException {
        if (b == 0) {
            throw new DivideByZeroException("除数不能为零：" + a + " / " + b);
        }
        return a / b;
    }
    
    public double calculate(double a, double b, String operator) 
            throws DivideByZeroException, InvalidOperatorException {
        
        if (operator == null) {
            throw new InvalidOperatorException("操作符不能为空");
        }
        
        switch (operator) {
            case "+":
                return add(a, b);
            case "-":
                return subtract(a, b);
            case "*":
                return multiply(a, b);
            case "/":
                return divide(a, b);
            default:
                throw new InvalidOperatorException("不支持的操作符：" + operator);
        }
    }
}
```

### 修复后的测试代码示例
```java
public static void main(String[] args) {
    Calculator calc = new Calculator();
    
    // 正常运算测试
    try {
        System.out.println("10 + 5 = " + calc.calculate(10, 5, "+"));
        System.out.println("10 - 5 = " + calc.calculate(10, 5, "-"));
        System.out.println("10 * 5 = " + calc.calculate(10, 5, "*"));
        System.out.println("10 / 5 = " + calc.calculate(10, 5, "/"));
    } catch (Exception e) {
        System.err.println("计算错误：" + e.getMessage());
    }
    
    // 除零异常测试
    try {
        double result = calc.calculate(10, 0, "/");
        System.out.println("结果：" + result);
    } catch (DivideByZeroException e) {
        System.err.println("除零错误：" + e.getMessage());
    } catch (InvalidOperatorException e) {
        System.err.println("操作符错误：" + e.getMessage());
    }
    
    // 无效操作符测试
    try {
        double result = calc.calculate(10, 5, "%");
        System.out.println("结果：" + result);
    } catch (InvalidOperatorException e) {
        System.err.println("操作符错误：" + e.getMessage());
    } catch (DivideByZeroException e) {
        System.err.println("除零错误：" + e.getMessage());
    }
}
```

## 📊 知识点掌握情况

| 知识点 | 掌握程度 | 说明 |
|--------|----------|------|
| 自定义异常类 | 🟡 基础 | 结构正确，需要完善构造方法 |
| 异常抛出 | 🔴 需改进 | throws声明错误，逻辑不正确 |
| 异常处理 | 🔴 需改进 | try-catch使用错误 |
| 方法返回值 | 🔴 需改进 | 调用方法但不返回结果 |
| switch语句 | 🔴 需改进 | 缺少return和default处理 |

## 🏆 总结

你的代码展现了对异常处理概念的**初步理解**，但在实现细节上存在严重问题：

- 🚨 **核心逻辑错误**：calculate方法完全不能正常工作
- 🚨 **异常处理错误**：没有正确抛出和处理自定义异常
- 🚨 **测试不完整**：没有验证功能是否正确

**建议**：按照修复版本重新实现，重点关注：
1. 正确的异常抛出和处理
2. 方法的返回值处理
3. 完整的测试用例

**这是一个很好的学习机会！修复这些问题后，你会对异常处理有更深入的理解！** 🚀

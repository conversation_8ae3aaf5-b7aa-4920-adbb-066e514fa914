package exercise3;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 练习3：学生成绩管理器
 * 
 * 要求实现：
 * 1. 使用List<Student>存储学生信息
 * 2. addStudent()、removeStudent()、findStudent()方法
 * 3. sortByScore()、getTopStudents()、calculateAverage()方法
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class ScoreManager {
    
    // TODO: 定义学生列表
    List<Student> studentList = new ArrayList<>();
    
    // TODO: 构造方法
    public ScoreManager() {}

    public ScoreManager(List<Student> animalList) {
        this.studentList = animalList;
    }
    
    
    // TODO: 添加学生
    public void addStudent(Student student) {
        // 你的实现
        studentList.add(student);
    }
    
    // TODO: 根据学号删除学生
    public boolean removeStudent(String id) {
        // 你的实现
        return studentList.remove(id);
    }
    
    // TODO: 根据学号查找学生
    public Student findStudent(String id) {
        // 你的实现
        for(Student student : studentList) {
            if(id.equals(student.getId())) {
                return student;
            }
        }
        return null;
    }
    
    // TODO: 按成绩排序
    public void sortByScore() {
        // 你的实现
        Collections.sort(studentList);
    }
    
    // TODO: 获取前n名学生
    public List<Student> getTopStudents(int n) {
        // 你的实现
        List<Student> list =  studentList.stream().limit(n).collect(Collectors.toList());
        return list;
    }
    
    // TODO: 计算平均分
    public double calculateAverage() {
        // 你的实现
        return studentList.stream().mapToDouble(Student::getScore).average().orElse(0);
    }
    
    // TODO: 显示所有学生
    public void displayAllStudents() {
        // 你的实现
        studentList.stream().forEach(System.out::println);
    }
    
    // TODO: 获取学生数量
    public int getStudentCount() {
        // 你的实现
        long count = studentList.stream().count();
        return (int) count;
    }
}

# Java基础练习题集 🎯

## 📋 练习说明
- **只提供题目要求，不提供答案代码**
- **你需要自己动手实现每个练习**
- **建议按顺序完成，每题约15-30分钟**
- **完成后可以要求我检查代码或提供参考答案**

---

## 🟢 练习1：面向对象基础 - 银行账户类

### 题目要求
设计一个银行账户类 `BankAccount`，实现以下功能：

**类的属性：**
- 账户号码（accountNumber，String类型）
- 账户余额（balance，double类型）
- 账户持有人姓名（holderName，String类型）

**类的方法：**
- 构造方法：初始化账户信息
- `deposit(double amount)`：存款方法
- `withdraw(double amount)`：取款方法（余额不足时输出提示）
- `getBalance()`：查询余额
- `toString()`：返回账户信息字符串

**测试要求：**
- 创建2个账户对象
- 进行存款、取款操作
- 输出最终账户信息

**文件位置：** `Ex/src/exercise1/BankAccount.java`

---

## 🟢 练习2：继承和多态 - 动物园管理

### 题目要求
设计动物园管理系统，体现继承和多态的使用：

**基类 Animal：**
- 属性：name（姓名），age（年龄）
- 方法：`makeSound()`（抽象方法），`eat()`，`toString()`

**子类要求：**
- `Dog`类：重写`makeSound()`输出"汪汪"
- `Cat`类：重写`makeSound()`输出"喵喵"
- `Bird`类：重写`makeSound()`输出"啾啾"

**Zoo类：**
- 属性：`List<Animal> animals`
- 方法：`addAnimal(Animal animal)`，`showAllAnimals()`，`makeAllSounds()`

**测试要求：**
- 创建不同类型的动物对象
- 添加到动物园中
- 调用`makeAllSounds()`展示多态效果

**文件位置：** `Ex/src/exercise2/`

---

## 🟢 练习3：集合框架 - 学生成绩管理

### 题目要求
使用集合框架管理学生成绩信息：

**Student类：**
- 属性：学号（id），姓名（name），成绩（score）
- 实现`equals()`和`hashCode()`方法
- 实现`Comparable<Student>`接口（按成绩排序）

**ScoreManager类：**
- 使用`List<Student>`存储学生信息
- `addStudent(Student student)`：添加学生
- `removeStudent(String id)`：根据学号删除学生
- `findStudent(String id)`：根据学号查找学生
- `sortByScore()`：按成绩排序
- `getTopStudents(int n)`：获取前n名学生
- `calculateAverage()`：计算平均分

**测试要求：**
- 添加至少5个学生
- 测试所有方法功能
- 输出排序后的学生列表

**文件位置：** `Ex/src/exercise3/`

---

## 🟢 练习4：异常处理 - 计算器程序

### 题目要求
设计一个安全的计算器程序，包含完善的异常处理：

**自定义异常：**
- `DivideByZeroException`：除零异常
- `InvalidOperatorException`：无效操作符异常

**Calculator类：**
- `add(double a, double b)`：加法
- `subtract(double a, double b)`：减法
- `multiply(double a, double b)`：乘法
- `divide(double a, double b)`：除法（处理除零异常）
- `calculate(double a, double b, String operator)`：通用计算方法

**CalculatorTest类：**
- 测试各种正常和异常情况
- 使用try-catch处理异常
- 输出友好的错误提示信息

**测试用例：**
- 正常的四则运算
- 除零操作
- 无效操作符
- 边界值测试

**文件位置：** `Ex/src/exercise4/`

---

## 🟢 练习5：泛型应用 - 通用容器类

### 题目要求
设计一个泛型容器类，实现类似ArrayList的基本功能：

**MyContainer<T>类：**
- 使用数组作为底层存储
- `add(T item)`：添加元素
- `get(int index)`：获取指定位置元素
- `remove(int index)`：删除指定位置元素
- `size()`：获取容器大小
- `isEmpty()`：判断是否为空
- `contains(T item)`：判断是否包含某元素
- `toArray()`：转换为数组

**扩容机制：**
- 初始容量为10
- 当容量不足时，扩容为原来的1.5倍

**测试要求：**
- 测试不同类型的泛型（String、Integer、自定义类）
- 测试扩容功能
- 测试边界情况（空容器操作等）

**文件位置：** `Ex/src/exercise5/`

---

## 🟢 练习6：枚举和内部类 - 扑克牌游戏（含大小王）

### 题目要求
设计扑克牌相关的类，使用枚举和内部类：

**枚举定义：**
- `Suit`：花色（红桃、黑桃、方块、梅花、王）
- `Rank`：点数（3-K、A、2、小王、大王），点数排序：3-K(3-13), A(14), 2(15), 小王(16), 大王(17)

**Card类：**
- 属性：花色（Suit枚举）、点数（Rank枚举）
- 方法：`toString()`，`getValue()`，`isJoker()`，`isRed()`，`isBlack()`
- 实现：`equals()`和`hashCode()`方法

**Deck类：**
- 包含54张牌的完整牌组（52张标准牌 + 2张王牌）
- `shuffle()`：洗牌
- `deal()`：发一张牌
- `reset()`：重置牌组
- `sortByValue()`：按点数排序
- 内部类`CardComparator`：实现牌的比较

**测试要求：**
- 创建完整54张牌组
- 洗牌并发牌
- 按点数排序（3-K, A, 2, 小王, 大王）
- 输出牌组信息
- 测试王牌的特殊功能

**文件位置：** `Ex/src/exercise6/`

---

## 🟢 练习7：Stream API - 数据分析

### 题目要求
使用Stream API分析员工数据：

**Employee类：**
- 属性：id、name、department、salary、age

**EmployeeAnalyzer类：**
使用Stream API实现以下分析功能：
- `getHighSalaryEmployees(double threshold)`：获取高薪员工
- `groupByDepartment()`：按部门分组
- `calculateAverageSalary()`：计算平均薪资
- `findOldestEmployee()`：找到年龄最大的员工
- `getSalaryStatistics()`：获取薪资统计信息（最高、最低、平均）
- `getTopNEmployeesByAge(int n)`：获取年龄最大的N个员工

**测试数据：**
- 创建至少10个不同部门的员工
- 测试所有分析功能
- 输出分析结果

**文件位置：** `Ex/src/exercise7/`

---

## 📝 提交指南

### 完成步骤
1. **创建对应的包和类文件**
2. **实现所有要求的功能**
3. **编写测试代码验证功能**
4. **确保代码能正常编译运行**

### 代码规范
- 使用合适的访问修饰符
- 添加必要的注释
- 遵循Java命名规范
- 处理可能的异常情况

### 检查清单
- [ ] 所有方法都已实现
- [ ] 测试代码覆盖主要功能
- [ ] 异常处理得当
- [ ] 代码格式规范
- [ ] 能够正常编译运行

---

## 🎯 学习建议

1. **逐题完成**：不要跳跃，按顺序完成每个练习
2. **独立思考**：先自己尝试实现，遇到困难再查资料
3. **测试验证**：每完成一个功能就测试一下
4. **代码优化**：完成基本功能后，考虑代码优化
5. **总结反思**：每题完成后总结用到的知识点

**完成任意练习后，可以告诉我题目编号，我来帮你检查代码或提供改进建议！** 💪

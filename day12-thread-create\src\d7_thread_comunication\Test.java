package d7_thread_comunication;

public class Test {

    public static void main(String[] args) {

        //1.竞争桌子
        Desk desk = new Desk();

        //2.创建两个消费进程
        new ConsumerThread(desk,"小懒猪").start();
        new ConsumerThread(desk,"大懒猪").start();


        //3.创建3个生产者线程
        new MakeThread(desk,"1").start();
        new MakeThread(desk,"2").start();
        new MakeThread(desk,"3").start();
    }


}

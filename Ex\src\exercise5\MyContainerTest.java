package exercise5;

/**
 * 练习5：泛型容器测试程序
 * 
 * 测试要求：
 * 1. 测试不同类型的泛型（String、Integer、自定义类）
 * 2. 测试扩容功能
 * 3. 测试边界情况（空容器操作等）
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class MyContainerTest {
    
    public static void main(String[] args) {
        System.out.println("=== 泛型容器测试 ===\n");
        
        // TODO: 测试String类型容器
        System.out.println("--- String类型容器测试 ---");
        testStringContainer();

        // TODO: 测试Integer类型容器
        System.out.println("\n--- Integer类型容器测试 ---");
        testIntegerContainer();

        // TODO: 测试扩容功能
        System.out.println("\n--- 扩容功能测试 ---");
        testCapacityExpansion();

        // TODO: 测试边界情况
        System.out.println("\n--- 边界情况测试 ---");
        testBoundaryConditions();

        // TODO: 测试自定义类型（可选）
        System.out.println("\n--- 自定义类型测试 ---");
        testCustomType();
        
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    // 测试String类型容器
    private static void testStringContainer() {
        MyContainer<String> stringContainer = new MyContainer<>();

        // 测试添加元素
        stringContainer.add("Hello");
        stringContainer.add("World");
        stringContainer.add("Java");

        System.out.println("容器内容: " + stringContainer);
        System.out.println("容器大小: " + stringContainer.size());
        System.out.println("是否为空: " + stringContainer.isEmpty());

        // 测试获取元素
        System.out.println("索引0的元素: " + stringContainer.get(0));
        System.out.println("索引1的元素: " + stringContainer.get(1));

        // 测试包含检查
        System.out.println("是否包含'Java': " + stringContainer.contains("Java"));
        System.out.println("是否包含'Python': " + stringContainer.contains("Python"));

        // 测试删除元素
        String removed = stringContainer.remove(1);
        System.out.println("删除的元素: " + removed);
        System.out.println("删除后容器: " + stringContainer);
    }

    // 测试Integer类型容器
    private static void testIntegerContainer() {
        MyContainer<Integer> intContainer = new MyContainer<>();

        // 添加数字
        for (int i = 1; i <= 5; i++) {
            intContainer.add(i * 10);
        }

        System.out.println("数字容器: " + intContainer);
        System.out.println("容器大小: " + intContainer.size());

        // 测试转换为数组
        Object[] array = intContainer.toArray();
        System.out.print("转换为数组: [");
        for (int i = 0; i < array.length; i++) {
            System.out.print(array[i]);
            if (i < array.length - 1) System.out.print(", ");
        }
        System.out.println("]");
    }

    // 测试扩容功能
    private static void testCapacityExpansion() {
        MyContainer<Integer> container = new MyContainer<>();

        System.out.println("测试扩容功能（初始容量10）:");

        // 添加超过初始容量的元素
        for (int i = 1; i <= 15; i++) {
            container.add(i);
            if (i == 10) {
                System.out.println("添加第10个元素后: " + container);
            }
        }

        System.out.println("添加15个元素后: " + container);
        System.out.println("容器大小: " + container.size());
        System.out.println("扩容成功！");
    }

    // 测试边界情况
    private static void testBoundaryConditions() {
        MyContainer<String> container = new MyContainer<>();

        // 测试空容器
        System.out.println("空容器测试:");
        System.out.println("是否为空: " + container.isEmpty());
        System.out.println("大小: " + container.size());
        System.out.println("容器内容: " + container);

        // 测试索引越界
        try {
            container.get(0);
        } catch (IndexOutOfBoundsException e) {
            System.out.println("正确捕获索引越界异常: " + e.getMessage());
        }

        // 添加null元素测试
        container.add(null);
        container.add("test");
        container.add(null);
        System.out.println("包含null的容器: " + container);
        System.out.println("是否包含null: " + container.contains(null));

        // 删除null元素
        container.remove(0);
        System.out.println("删除第一个null后: " + container);
    }

    // 测试自定义类型
    private static void testCustomType() {
        MyContainer<TestItem> itemContainer = new MyContainer<>();

        // 添加自定义对象
        itemContainer.add(new TestItem("Item1"));
        itemContainer.add(new TestItem("Item2"));
        itemContainer.add(new TestItem("Item3"));

        System.out.println("自定义类型容器: " + itemContainer);
        System.out.println("容器大小: " + itemContainer.size());

        // 测试包含检查（需要equals方法）
        TestItem searchItem = new TestItem("Item2");
        System.out.println("是否包含Item2: " + itemContainer.contains(searchItem));

        // 测试获取和删除
        TestItem firstItem = itemContainer.get(0);
        System.out.println("第一个元素: " + firstItem);

        TestItem removedItem = itemContainer.remove(1);
        System.out.println("删除的元素: " + removedItem);
        System.out.println("删除后容器: " + itemContainer);
    }

    // TODO: 可以定义一个简单的测试用类
    static class TestItem {
        private String name;

        public TestItem(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return "TestItem{" + name + "}";
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TestItem testItem = (TestItem) obj;
            return name != null ? name.equals(testItem.name) : testItem.name == null;
        }
    }
}

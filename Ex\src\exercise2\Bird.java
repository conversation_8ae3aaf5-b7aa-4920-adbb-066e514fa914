package exercise2;

/**
 * 练习2：鸟类 - 继承Animal
 * 
 * 要求：
 * 1. 继承Animal类
 * 2. 重写makeSound()方法，输出"啾啾"
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class Bird extends Animal {
    
    // TODO: 构造方法
    public Bird(String name, int age) {
        super(name, age);
    }
    
    // TODO: 重写makeSound方法
    @Override
    public void makeSound() {
        // 你的实现
        System.out.println("啾啾");
    }
}

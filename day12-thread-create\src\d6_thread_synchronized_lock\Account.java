package d6_thread_synchronized_lock;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Data
@NoArgsConstructor
public class Account {
    private String cardId;
    private double money;

    public Account(String cardId, double money){
        this.cardId = cardId;
        this.money = money;
    }

    private final Lock lk = new ReentrantLock();


    public static void test(){
        //静态变量使用类名.class  作为锁
        synchronized (Account.class){
        }
    }

    //上锁
    public void drawMoney(double money){
        String name = Thread.currentThread().getName();
        lk.lock();
        try {
            if(this.money >= money){
                //更容易出现安全问题
                System.out.println(name + "来取钱了，" + name + ": " + this.money);
                this.money -= money;
                System.out.println(name + "取钱成功，账户余额: " + this.money);

            } else {
                System.out.println(name + "余额不足: " + this.money);
            }
        }finally {
            lk.unlock();
        }
    }
}

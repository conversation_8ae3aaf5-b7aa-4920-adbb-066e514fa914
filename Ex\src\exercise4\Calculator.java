package exercise4;

/**
 * 练习4：安全计算器类
 * 
 * 要求实现：
 * 1. 四则运算方法（add、subtract、multiply、divide）
 * 2. 通用计算方法calculate()
 * 3. 完善的异常处理机制
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class Calculator {
    
    // TODO: 加法运算
    public double add(double a, double b) {
        // 你的实现
        return a+b;
    }
    
    // TODO: 减法运算
    public double subtract(double a, double b) {
        // 你的实现return a-b;
        return a-b;
    }
    
    // TODO: 乘法运算
    public double multiply(double a, double b) {
        // 你的实现
        return a*b;
    }
    
    // TODO: 除法运算（需要处理除零异常）
    public double divide(double a, double b) throws DivideByZeroException {
        // 你的实现
        if(b==0){
            throw new DivideByZeroException("除数不能为0");
        }
        return a/b;
    }
    
    // TODO: 通用计算方法
    public double calculate(double a, double b, String operator) 
            throws DivideByZeroException, InvalidOperatorException {
        // 你的实现
        // 支持的操作符：+、-、*、/
        switch (operator) {
            case "/":
                  return divide(a, b);
            case "*":
                return multiply(a,b);
            case "-":
                return  subtract(a,b);

            case "+":
                return add(a,b);
            default:
                throw new InvalidOperatorException("操作不在此");

        }

    }
    
    // TODO: 可以添加其他辅助方法
    
}

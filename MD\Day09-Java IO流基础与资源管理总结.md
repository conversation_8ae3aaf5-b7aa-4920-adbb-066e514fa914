# Day09 Java IO流基础与资源管理总结

## 📚 学习目标

通过day09-io-code的学习，掌握Java IO流的基础操作和资源管理：
1. **字节输入流** - FileInputStream的使用方法
2. **字节输出流** - FileOutputStream的写入操作
3. **文件复制** - 字节流实现文件复制功能
4. **finally语句** - 确保资源正确释放
5. **try-with-resources** - 自动资源管理机制

## 📥 字节输入流 - FileInputStream

### 核心概念
- FileInputStream是文件字节输入流，用于从文件中读取字节数据
- 继承自InputStream抽象类
- 适用于读取任何类型的文件（文本、图片、视频等）
- 读取方式：单字节读取、字节数组读取、一次性读取

### 1. 单字节读取 (FileInputStreamDemo1)

```java
public class FileInputStreamDemo1 {
    public static void main(String[] args) throws Exception {
        // 1. 创建文件字节输入流与目标文件接通
        InputStream is = new FileInputStream("day09-io-code/src/dei01.txt");
        
        // 2. 循环读取每个字节
        int b; // 存储读取的字节
        while((b = is.read()) != -1) {
            System.out.println((char)b);
        }
        
        // 3. 关闭流释放资源
        is.close();
    }
}
```

**特点分析**：
- **优点**：简单易懂，逐字节处理
- **缺点**：性能差，中文字符会乱码（字节截断）
- **适用场景**：小文件的简单读取

### 2. 字节数组读取 (FileInputStreamDemo2)

```java
public class FileInputStreamDemo2 {
    public static void main(String[] args) throws Exception {
        InputStream is = new FileInputStream("day09-io-code/src/dei02.txt");
        
        // 使用字节数组作为缓冲区
        byte[] bytes = new byte[3];
        int len; // 实际读取的字节数
        
        while((len = is.read(bytes)) != -1) {
            // 只输出实际读取的字节数，避免缓冲区残留数据
            System.out.print(new String(bytes, 0, len));
        }
        
        is.close();
    }
}
```

**核心要点**：
- **缓冲区大小**：通常设置为1024字节（1KB）或其倍数
- **实际读取长度**：使用`new String(bytes, 0, len)`避免缓冲区残留
- **性能提升**：批量读取比单字节读取效率高
- **乱码问题**：仍然存在中文字符截断问题

### 3. 一次性读取 (FileInputStreamDemo3)

```java
public class FileInputStreamDemo3 {
    public static void main(String[] args) throws Exception {
        InputStream is = new FileInputStream("day09-io-code/src/dei03.txt");
        
        // 方式一：根据文件大小创建字节数组
        File f = new File("day09-io-code/src/dei03.txt");
        byte[] buffer = new byte[(int)f.length()];
        int len = is.read(buffer);
        System.out.println("读取到的字节数：" + len);
        System.out.println(new String(buffer));
        
        // 方式二：使用readAllBytes()方法（JDK9+）
        byte[] bytes = is.readAllBytes();
        System.out.println(new String(bytes));
        
        is.close();
    }
}
```

**适用场景**：
- **小文件**：文件大小在内存可承受范围内
- **完整读取**：需要一次性获取全部文件内容
- **注意事项**：大文件会导致内存溢出

## 📤 字节输出流 - FileOutputStream

### 核心概念
- FileOutputStream是文件字节输出流，用于向文件写入字节数据
- 继承自OutputStream抽象类
- 写入模式：覆盖写入、追加写入
- 写入方式：单字节写入、字节数组写入

### 1. 字节输出流基本操作 (FileOutputStreamDemo4)

```java
public class FileOutputStreamDemo4 {
    public static void main(String[] args) throws Exception {
        // 1. 创建文件字节输出流（追加模式）
        FileOutputStream os = new FileOutputStream("day09-io-code/src/dei04.txt", true);
        
        // 2. 写入单个字节
        os.write('a');      // 写入字符
        os.write(97);       // 写入ASCII码值
        
        // 3. 写入换行符
        os.write("\r\n".getBytes());
        
        // 4. 写入字节数组
        byte[] bytes = "你是一个入门级程序员".getBytes();
        os.write(bytes);
        
        // 5. 写入字节数组的部分内容
        // 从索引21开始，写入9个字节
        os.write(bytes, 21, 9);
        
        // 6. 关闭流（包含刷新操作）
        os.close();
    }
}
```

**核心方法**：
- **write(int b)**：写入单个字节
- **write(byte[] bytes)**：写入整个字节数组
- **write(byte[] bytes, int off, int len)**：写入字节数组的指定部分
- **flush()**：刷新缓冲区到磁盘
- **close()**：关闭流并刷新缓冲区

**写入模式**：
- **覆盖模式**：`new FileOutputStream("file.txt")`
- **追加模式**：`new FileOutputStream("file.txt", true)`

## 📋 文件复制实战 - 字节流应用

### 1. 基础文件复制 (CopyTest5)

```java
public class CopyTest5 {
    public static void main(String[] args) {
        try {
            // 创建输入流与源文件接通
            InputStream is = new FileInputStream("D:\\...\\image_01.png");
            
            // 创建输出流与目标文件接通
            OutputStream os = new FileOutputStream("D:\\...\\image_01_copy.png");
            
            // 使用缓冲区进行复制
            byte[] buffer = new byte[1024]; // 1KB缓冲区
            int len;
            
            while((len = is.read(buffer)) != -1) {
                os.write(buffer, 0, len);
            }
            
            // 关闭流
            os.close();
            is.close();
            
            System.out.println("复制成功");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

**复制原理**：
1. **读取源文件**：使用FileInputStream读取源文件字节
2. **写入目标文件**：使用FileOutputStream写入目标文件
3. **缓冲区传输**：使用字节数组作为中转缓冲区
4. **循环处理**：循环读取-写入直到文件结束

**适用文件类型**：
- **文本文件**：.txt、.java、.xml等
- **图片文件**：.png、.jpg、.gif等
- **音视频文件**：.mp3、.mp4、.avi等
- **任何二进制文件**：字节流可以复制任何类型的文件

## 🔒 资源管理 - finally与try-with-resources

### 核心概念
- IO流属于系统资源，使用后必须正确释放
- 不正确的资源管理会导致内存泄漏和文件句柄耗尽
- Java提供了多种资源管理机制确保资源正确释放

### 1. finally语句块 (FinallyDemo1)

```java
public class FinallyDemo1 {
    public static void main(String[] args) {
        try {
            System.out.println(10/0); // 故意制造异常
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            System.out.println("finally=========");
        }
    }
}
```

**finally特点**：
- **必执行性**：无论是否发生异常都会执行
- **执行时机**：try或catch执行完毕后执行
- **唯一例外**：JVM崩溃时不执行
- **注意事项**：不要在finally中返回数据

### 2. 手动资源管理 (FinallyDemo2)

```java
public class FinallyDemo2 {
    public static void main(String[] args) {
        InputStream is = null;
        OutputStream os = null;
        
        try {
            is = new FileInputStream("source.png");
            os = new FileOutputStream("target.png");
            
            // 文件复制逻辑
            byte[] buffer = new byte[1024];
            int len;
            while((len = is.read(buffer)) != -1) {
                os.write(buffer, 0, len);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 在finally中关闭资源
            try {
                if (os != null) os.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            try {
                if (is != null) is.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            System.out.println("复制成功");
        }
    }
}
```

**手动管理问题**：
- **代码冗长**：需要大量的try-catch嵌套
- **容易遗漏**：可能忘记关闭某些资源
- **异常处理复杂**：关闭资源时也可能抛出异常

### 3. 自动资源管理 (FinallyDemo3)

```java
public class FinallyDemo3 {
    public static void main(String[] args) {
        // try-with-resources语法
        try(
            // 在try()中声明需要自动管理的资源
            InputStream is = new FileInputStream("source.png");
            OutputStream os = new FileOutputStream("target.png");
        ) {
            // 文件复制逻辑
            byte[] buffer = new byte[1024];
            int len;
            while((len = is.read(buffer)) != -1) {
                os.write(buffer, 0, len);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 资源会自动关闭，无需手动close()
    }
}
```

**try-with-resources优势**：
- **自动关闭**：资源使用完毕后自动调用close()方法
- **代码简洁**：无需手动编写finally块
- **异常安全**：即使发生异常也能正确关闭资源
- **多资源支持**：可以同时管理多个资源

**使用条件**：
- 资源类必须实现`AutoCloseable`接口
- 大部分IO流都实现了该接口
- JDK7+支持此语法

## 📊 字节流读取方式对比

| 读取方式 | 性能 | 内存占用 | 适用场景 | 中文处理 |
|---------|------|----------|----------|----------|
| 单字节读取 | 低 | 极小 | 小文件简单处理 | 会乱码 |
| 字节数组读取 | 中 | 中等 | 一般文件处理 | 会乱码 |
| 一次性读取 | 高 | 大 | 小文件完整读取 | 会乱码 |

## 🔧 IO流使用最佳实践

### 1. 缓冲区大小选择
```java
// 推荐的缓冲区大小
byte[] buffer = new byte[1024];      // 1KB - 小文件
byte[] buffer = new byte[8192];      // 8KB - 中等文件
byte[] buffer = new byte[1024 * 64]; // 64KB - 大文件
```

### 2. 异常处理模式
```java
// 推荐：使用try-with-resources
try(InputStream is = new FileInputStream("file.txt")) {
    // 处理逻辑
} catch (IOException e) {
    e.printStackTrace();
}

// 不推荐：手动管理资源
InputStream is = null;
try {
    is = new FileInputStream("file.txt");
    // 处理逻辑
} finally {
    if (is != null) {
        try {
            is.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
```

### 3. 文件复制优化
```java
public static void copyFile(String source, String target) {
    try(InputStream is = new FileInputStream(source);
        OutputStream os = new FileOutputStream(target)) {

        byte[] buffer = new byte[8192]; // 8KB缓冲区
        int len;
        while((len = is.read(buffer)) != -1) {
            os.write(buffer, 0, len);
        }

    } catch (IOException e) {
        throw new RuntimeException("文件复制失败", e);
    }
}
```

## ⚠️ 常见问题与解决方案

### 1. 中文乱码问题
**问题**：字节流读取中文文件时出现乱码
```java
// 问题代码
InputStream is = new FileInputStream("chinese.txt");
int b;
while((b = is.read()) != -1) {
    System.out.print((char)b); // 中文会乱码
}
```

**解决方案**：使用字符流或指定编码
```java
// 解决方案1：使用字符流（后续课程）
// 解决方案2：一次性读取后指定编码
byte[] bytes = Files.readAllBytes(Paths.get("chinese.txt"));
String content = new String(bytes, "UTF-8");
```

### 2. 资源泄漏问题
**问题**：忘记关闭流导致资源泄漏
```java
// 危险代码
InputStream is = new FileInputStream("file.txt");
// 处理逻辑...
// 忘记调用is.close()
```

**解决方案**：使用try-with-resources
```java
// 安全代码
try(InputStream is = new FileInputStream("file.txt")) {
    // 处理逻辑...
} // 自动关闭
```

### 3. 文件不存在异常
**问题**：文件路径错误或文件不存在
```java
// 可能抛出FileNotFoundException
InputStream is = new FileInputStream("nonexistent.txt");
```

**解决方案**：先检查文件存在性
```java
File file = new File("file.txt");
if (file.exists() && file.isFile()) {
    try(InputStream is = new FileInputStream(file)) {
        // 处理逻辑...
    }
} else {
    System.out.println("文件不存在或不是文件");
}
```

## 🎯 学习总结

### 核心知识点
1. **FileInputStream**：文件字节输入流，支持多种读取方式
2. **FileOutputStream**：文件字节输出流，支持覆盖和追加模式
3. **文件复制**：字节流可以复制任何类型的文件
4. **finally语句**：确保代码块必定执行，常用于资源清理
5. **try-with-resources**：自动资源管理，简化代码并提高安全性

### 字节流特点
- **通用性强**：可以处理任何类型的文件
- **性能较好**：直接操作字节，效率高
- **中文问题**：处理文本文件时可能出现乱码
- **适用场景**：文件复制、图片处理、音视频处理

### 资源管理原则
- **及时关闭**：使用完毕后立即关闭流
- **异常安全**：即使发生异常也要确保资源关闭
- **优先选择**：try-with-resources > finally > 手动管理
- **多资源管理**：按照创建顺序的逆序关闭

### 最佳实践
- **缓冲区使用**：合理选择缓冲区大小提高性能
- **异常处理**：使用try-with-resources简化资源管理
- **路径处理**：使用相对路径或配置文件管理路径
- **编码意识**：了解字节流的局限性，适时选择字符流

Day09奠定了Java IO编程的基础，掌握了字节流的基本操作和资源管理机制，为后续学习字符流、缓冲流等高级IO特性打下了坚实基础。

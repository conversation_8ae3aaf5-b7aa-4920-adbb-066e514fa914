package exercise3;

/**
 * 练习3：学生成绩管理测试
 * 
 * 测试要求：
 * 1. 添加至少5个学生
 * 2. 测试所有方法功能
 * 3. 输出排序后的学生列表
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class ScoreManagerTest {
    
    public static void main(String[] args) {
        System.out.println("=== 学生成绩管理系统测试 ===\n");
        
        // TODO: 创建成绩管理器
        ScoreManager scoreManager = new ScoreManager();
        
        
        // TODO: 添加学生数据
        System.out.println("--- 添加学生 ---");
        // 提示：添加至少5个不同成绩的学生
        scoreManager.studentList.add(new Student("1","2",20));
        scoreManager.studentList.add(new Student("2","3",30));
        scoreManager.studentList.add(new Student("3","4",40));
        scoreManager.studentList.add(new Student("4","5",50));
        scoreManager.studentList.add(new Student("5","6",60));

        
        // TODO: 显示所有学生
        System.out.println("\n--- 所有学生信息 ---");
        scoreManager.displayAllStudents();
        
        
        // TODO: 测试查找功能
        System.out.println("\n--- 查找学生测试 ---");
        scoreManager.findStudent("2");
        
        
        // TODO: 测试排序功能
        System.out.println("\n--- 按成绩排序 ---");
        scoreManager.sortByScore();
        scoreManager.displayAllStudents();
        
        
        // TODO: 测试获取前n名
        System.out.println("\n--- 前3名学生 ---");
        scoreManager.getTopStudents(3);
        
        // TODO: 计算平均分
        System.out.println("\n--- 成绩统计 ---");
        scoreManager.calculateAverage();
        
        // TODO: 测试删除功能
        System.out.println("\n--- 删除学生测试 ---");
        scoreManager.removeStudent("3");
        
        
        System.out.println("\n=== 测试完成 ===");
    }
}

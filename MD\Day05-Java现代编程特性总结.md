# Day05 Java现代编程特性总结

## 📚 学习目标

通过day5-api的学习，掌握Java现代编程的核心特性：
1. **数组操作进阶** - Arrays工具类与函数式编程结合
2. **Lambda表达式** - 从匿名内部类到现代函数式编程
3. **方法引用** - Lambda表达式的进一步简化
4. **排序算法基础** - 传统算法实现
5. **正则表达式** - 强大的文本处理工具

## 🔧 数组操作进阶 - Arrays工具类

### 核心概念
- Arrays是Java提供的数组操作工具类
- 提供数组的打印、拷贝、扩容、排序等功能
- setAll方法结合函数式接口，展示现代Java编程风格

### 1. 基础操作 (ArrayDemo1)

```java
import java.util.Arrays;
import java.util.function.IntToDoubleFunction;

public class ArrayDemo1 {
    public static void main(String[] args) {
        int[] arr = {11,55,33,22,98};
        
        // 1. 数组转字符串
        String result = Arrays.toString(arr);
        System.out.println(result); // [11, 55, 33, 22, 98]

        // 2. 拷贝数组的内容到一个新数组，并返回新数组
        // public static 类型[] copyOfRange(类型[]原数组，开始索引，结束索引)
        int[] arr2 = Arrays.copyOfRange(arr,1,4); // 包前不包后
        System.out.println(Arrays.toString(arr2)); // [55, 33, 22]

        // 3. 给数组扩容
        // public static 类型[] copyOf(类型[]原数组，新数组长度)
        int[] arr3 = Arrays.copyOf(arr,10);
        System.out.println(Arrays.toString(arr3)); // [11, 55, 33, 22, 98, 0, 0, 0, 0, 0]

        // 4. 数组排序 - 升序排序 快排
        Arrays.sort(arr);
        System.out.println(Arrays.toString(arr)); // [11, 22, 33, 55, 98]

        // 5. 修改数组中每个数据，再存入 - 函数式编程
        double[] score = {99.5,90,59.5,78,98,55};
        // 需求：给每个分数都加10分
        Arrays.setAll(score, new IntToDoubleFunction() {
            @Override
            public double applyAsDouble(int index) {
                return score[index] + 10;
            }
        });
        System.out.println(Arrays.toString(score)); // [109.5, 100.0, 69.5, 88.0, 108.0, 65.0]
    }
}
```

**运行结果**：
```
[11, 55, 33, 22, 98]
[55, 33, 22]
[11, 55, 33, 22, 98, 0, 0, 0, 0, 0]
[11, 22, 33, 55, 98]
[109.5, 100.0, 69.5, 88.0, 108.0, 65.0]
```

## 🚀 Lambda表达式 - 现代函数式编程

### 核心概念
- Lambda表达式是Java 8引入的重要特性
- 只能简化函数式接口的匿名内部类
- 函数式接口：有且仅有一个抽象方法的接口
- 通过上下文推断参数类型和返回值类型

### 1. 从匿名内部类到Lambda表达式 (LambdaTest1)

```java
public class LambdaTest1 {
    public static void main(String[] args) {
        // 匿名内部类
        Animal a = new Animal(){
            @Override
            public void run(){
                System.out.println("动物跑");
            }
        };
        a.run(); // 动物跑

        // 传统匿名内部类实现函数式接口
        Swimming s2 = new Swimming(){
            @Override
            public void swin() {
                System.out.println("跳水里边喝边游泳~~~~~~~");
            }
        };
        s2.swin(); // 跳水里边喝边游泳~~~~~~~

        // Lambda表达式简化函数式接口
        // lambda并不能简化所有匿名内部类的代码，只能简化函数式接口的匿名内部类
        Swimming s = () -> {
            System.out.println("跳水里边喝边游泳");
        };
        s.swin(); // 跳水里边喝边游泳
    }
}

@FunctionalInterface    // 函数式接口 中有且仅有一个抽象方法
interface Swimming{
    void swin();
}

abstract class Animal{
    public abstract void run();
}
```

**运行结果**：
```
动物跑
跳水里边喝边游泳~~~~~~~
跳水里边喝边游泳
```

### 2. Lambda表达式的渐进式简化 (LambdaTest2)

```java
public class LambdaTest2 {
    public static void main(String[] args) {
        double[] score = {99.5,90,59.5,78,98,55};
        
        // Lambda表达式简化setAll操作
        Arrays.setAll(score, index -> {
            return score[index] + 10;
        });
        
        // 进一步简化 - 省略大括号和return
        Arrays.setAll(score, index -> score[index] + 10);
        
        System.out.println(Arrays.toString(score)); // [119.5, 110.0, 79.5, 98.0, 118.0, 75.0]

        // 学生数组排序的Lambda表达式演进
        Student[] students = new Student[4];
        students[0] = new Student("张三",18,'男',1.75);
        students[1] = new Student("李四",22,'男',1.80);
        students[2] = new Student("王五",20,'男',1.85);
        students[3] = new Student("赵六",19,'男',1.90);

        // 1. 传统匿名内部类
        Arrays.sort(students, new Comparator<Student>() {
            @Override
            public int compare(Student o1, Student o2) {
                return o1.getAge() - o2.getAge();
            }
        });

        // 2. Lambda表达式 - 保留类型声明
        Arrays.sort(students, (Student o1, Student o2) -> {
            return o1.getAge() - o2.getAge();
        });

        // 3. Lambda表达式 - 类型推断
        Arrays.sort(students, (o1, o2) -> {
            return o1.getAge() - o2.getAge();
        });

        // 4. Lambda表达式 - 最简形式
        Arrays.sort(students, (o1, o2) -> o1.getAge() - o2.getAge());

        System.out.println(Arrays.toString(students));
    }
}
```

**运行结果**：
```
[119.5, 110.0, 79.5, 98.0, 118.0, 75.0]
======================================
[Student{name='张三', age=18, gender=男, height=1.75}
, Student{name='赵六', age=19, gender=男, height=1.9}
, Student{name='王五', age=20, gender=男, height=1.85}
, Student{name='李四', age=22, gender=男, height=1.8}
]
```

## 🔗 方法引用 - Lambda表达式的极致简化

### 核心概念
- 方法引用是Lambda表达式的进一步简化
- 当Lambda表达式只是调用一个方法时，可以使用方法引用
- 四种方法引用类型：静态方法引用、实例方法引用、特定类型方法引用、构造器引用

### 1. 静态方法引用 (Test1)

```java
public class Test1 {
    public static void main(String[] args) {
        Student[] students = new Student[4];
        students[0] = new Student("张三",18,'男',1.75);
        students[1] = new Student("李四",22,'男',1.80);
        students[2] = new Student("王五",20,'男',1.85);
        students[3] = new Student("赵六",19,'男',1.90);

        // 三种表达同一个意思
        Arrays.sort(students,((o1, o2) -> Double.compare((o1.getHeight()), o2.getHeight())));
        Arrays.sort(students,((o1, o2) -> Student.compareStudentByHeight(o1,o2)));
        
        // 如果某个lambda表达式只是调用一个静态方法，并且前后参数一致，就可以使用静态方法引用
        Arrays.sort(students, Student::compareStudentByHeight);
    }
}
```

### 2. 实例方法引用 (Test2)

```java
public class Test2 {
    public static void main(String[] args) {
        Student[] students = new Student[4];
        // ... 初始化学生数组

        Test2 t = new Test2();
        
        // Lambda表达式调用实例方法
        Arrays.sort(students,(o1, o2) -> t.compare(o1,o2));
        
        // 实例方法引用
        Arrays.sort(students,t::compare);
    }

    public int compare(Student o1,Student o2){
        return Double.compare(o1.getHeight(), o2.getHeight());
    }
}
```

### 3. 特定类型的方法引用 (Test3)

```java
public class Test3 {
    public static void main(String[] args) {
        String[] name = {
            "Alice", "Patricia", "Charlie", "Diana", "Edward",
            "Fiona", "George", "Hannah", "Ian", "Julia"
        };

        // 如果某个lambda表达式只是调用一个实例方法，并且前面参数列表中的第一个参数是作为调用的主调，
        // 后面所有参数都是作为该实例方法的入参的，则此时就可以使用特定类型的方法引用
        Arrays.sort(name, (o1, o2) -> o1.compareToIgnoreCase(o2));
        
        // 特定类型的方法引用
        Arrays.sort(name, String::compareToIgnoreCase);

        System.out.println(Arrays.toString(name));
    }
}
```

**运行结果**：
```
[Alice, Charlie, Diana, Edward, Fiona, George, Hannah, Ian, Julia, Patricia]
```

### 4. 构造器引用 (Test4)

```java
public class Test4 {
    public static void main(String[] args) {
        // 如果某个lambda表达式里只是创建对象，并且前后参数一样，就可以使用构造器引用
        Create c2 = name -> new Car(name);
        
        // 构造器引用
        Create c2 = Car::new;

        Car car2 = c2.create("奔驰");
        System.out.println(car2.getName()); // 奔驰
    }
}

@FunctionalInterface
interface Create{
    Car create(String name);
}

class Car{
    private String name;
    
    public Car(){}
    
    public Car(String name){
        this.name = name;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
}
```

**运行结果**：
```
奔驰
```

## 🔢 排序算法基础 (d4_sf)

### 传统排序算法实现

```java
public class Demo {
    public static void main(String[] args) {
        // 目标: 完成排序
        int[] a = {5,2,3,1};

        // 选择排序算法实现
        for(int i = 0 ; i < a.length - 1 ; i++){
            int tmp = a[i];
            for(int j = i + 1; j < a.length; j++){
                if(a[j] < tmp){
                    a[i] = a[j];
                    a[j] = tmp;
                }
            }
        }
        // 排序完成，数组变为 [1, 2, 3, 5]
    }
}
```

## 📝 正则表达式 - 强大的文本处理工具

### 核心概念
- 正则表达式是用于匹配字符串的强大工具
- 相比传统字符串处理，正则表达式更简洁、更强大
- 广泛应用于数据验证、文本搜索、字符串替换等场景

### 1. 传统验证vs正则表达式 (regexTest1)

```java
public class regexTest1 {
    public static void main(String[] args) {
        System.out.println(checkQQ2("6543135")); // true
        System.out.println(checkQQ("af5446"));   // false
    }

    // 正则表达式验证QQ号
    public static boolean checkQQ2(String qq){
       return qq != null && qq.matches("[1-9]\\d{5,}");
    }

    // 传统方法验证QQ号
    public static boolean checkQQ(String qq){
        if(qq == null || qq.startsWith("0") || qq.length() <= 5) return false;

        for(int i = 0; i < qq.length(); i++){
            char ch = qq.charAt(i);
            if(ch < '0' || ch > '9'){
                return false;
            }
        }
        return true;
    }
}
```

**运行结果**：
```
true
false
```

### 2. 正则表达式完整规则体系 (regexTest2)

```java
public class regexTest2 {
    public static void main(String[] args) {
        /*
         * ==================== 正则表达式完整规则 ====================
         *
         * 1. 字符匹配
         * .          匹配任意单个字符（除换行符外）
         * [abc]      匹配字符集中的任意一个字符
         * [^abc]     匹配不在字符集中的任意字符
         * [a-z]      匹配范围内的任意字符
         * [A-Z]      匹配大写字母
         * [0-9]      匹配数字
         * [a-zA-Z]   匹配字母
         * [a-zA-Z0-9] 匹配字母和数字
         *
         * 2. 预定义字符类
         * \d         匹配数字 [0-9]
         * \D         匹配非数字 [^0-9]
         * \w         匹配单词字符 [a-zA-Z0-9_]
         * \W         匹配非单词字符 [^a-zA-Z0-9_]
         * \s         匹配空白字符（空格、制表符、换行符等）
         * \S         匹配非空白字符
         *
         * 3. 量词（Quantifiers）
         * *          匹配前面的字符0次或多次
         * +          匹配前面的字符1次或多次
         * ?          匹配前面的字符0次或1次
         * {n}        匹配前面的字符恰好n次
         * {n,}       匹配前面的字符至少n次
         * {n,m}      匹配前面的字符n到m次
         *
         * 4. 位置锚点
         * ^          匹配字符串开始位置
         * $          匹配字符串结束位置
         * \b         匹配单词边界
         * \B         匹配非单词边界
         *
         * 5. 分组和捕获
         * ()         分组，捕获匹配的内容
         * (?:...)    非捕获分组
         * |          或操作符（选择）
         */

        // 常用正则表达式示例
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";      // 邮箱验证
        String phoneRegex = "^1[3-9]\\d{9}$";                                          // 手机号验证
        String idCardRegex = "^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$"; // 身份证号验证
        String passwordRegex = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$"; // 密码强度验证

        System.out.println("正则表达式规则说明已完成！");
    }
}
```

### 3. 正则表达式系统演示 (RegexRulesDemo)

```java
public class RegexRulesDemo {
    public static void main(String[] args) {
        System.out.println("=============== 正则表达式完整规则演示 ===============\n");

        // 1. 基本字符匹配
        demonstrateBasicMatching();

        // 2. 字符类演示
        demonstrateCharacterClasses();

        // 3. 量词演示
        demonstrateQuantifiers();

        // 4. 位置锚点演示
        demonstrateAnchors();

        // 5. 分组和选择演示
        demonstrateGroupsAndAlternation();

        // 6. 实际应用示例
        demonstratePracticalExamples();
    }

    private static void demonstrateBasicMatching() {
        System.out.println("1. 基本字符匹配:");

        // 点号匹配任意字符
        testRegex("a.c", "abc", "adc", "a1c", "a c");

        // 字符集匹配
        testRegex("[abc]", "a", "b", "c", "d");

        // 否定字符集
        testRegex("[^abc]", "a", "d", "1", "!");

        // 范围匹配
        testRegex("[a-z]", "a", "m", "z", "A", "1");

        System.out.println();
    }

    private static void demonstratePracticalExamples() {
        System.out.println("6. 实际应用示例:");

        // 邮箱验证
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        System.out.println("邮箱验证:");
        testRegex(emailRegex, "<EMAIL>", "<EMAIL>", "invalid-email", "user@");

        // 手机号验证
        String phoneRegex = "^1[3-9]\\d{9}$";
        System.out.println("手机号验证:");
        testRegex(phoneRegex, "13812345678", "15987654321", "12345678901", "1381234567");

        // 密码强度验证
        String passwordRegex = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$";
        System.out.println("密码强度验证:");
        testRegex(passwordRegex, "Password123!", "password123", "PASSWORD123!", "Pass123!");

        System.out.println();
    }

    private static void testRegex(String regex, String... testStrings) {
        Pattern pattern = Pattern.compile(regex);
        System.out.printf("正则: %-20s ", regex);

        for (String test : testStrings) {
            Matcher matcher = pattern.matcher(test);
            boolean matches = matcher.matches();
            System.out.printf("'%s':%s ", test, matches ? "✓" : "✗");
        }
        System.out.println();
    }
}
```

**运行结果**（部分展示）：
```
=============== 正则表达式完整规则演示 ===============

1. 基本字符匹配:
正则: a.c                  'abc':✓ 'adc':✓ 'a1c':✓ 'a c':✓
正则: [abc]                'a':✓ 'b':✓ 'c':✓ 'd':✗
正则: [^abc]               'a':✗ 'd':✓ '1':✓ '!':✓
正则: [a-z]                'a':✓ 'm':✓ 'z':✓ 'A':✗ '1':✗

6. 实际应用示例:
邮箱验证:
正则: ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ '<EMAIL>':✓ '<EMAIL>':✓ 'invalid-email':✗ 'user@':✗
手机号验证:
正则: ^1[3-9]\d{9}$        '13812345678':✓ '15987654321':✓ '12345678901':✗ '1381234567':✗
密码强度验证:
正则: ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$ 'Password123!':✓ 'password123':✗ 'PASSWORD123!':✗ 'Pass123!':✗
```

## 🔄 技术演进路径

### 从传统到现代的编程思维转变

```
传统匿名内部类 → Lambda表达式 → 方法引用 → 函数式编程思想
```

**1. 匿名内部类时代**：
```java
Arrays.sort(students, new Comparator<Student>() {
    @Override
    public int compare(Student o1, Student o2) {
        return o1.getAge() - o2.getAge();
    }
});
```

**2. Lambda表达式时代**：
```java
Arrays.sort(students, (o1, o2) -> o1.getAge() - o2.getAge());
```

**3. 方法引用时代**：
```java
Arrays.sort(students, Student::compareByAge);
```

## 💡 实践应用总结

### Arrays工具类适用场景
- **数组打印**：`Arrays.toString()` 快速查看数组内容
- **数组拷贝**：`Arrays.copyOf()` 和 `Arrays.copyOfRange()` 安全拷贝
- **数组排序**：`Arrays.sort()` 高效排序
- **数组修改**：`Arrays.setAll()` 函数式批量修改

### Lambda表达式适用场景
- **集合操作**：排序、过滤、映射等操作
- **事件处理**：GUI事件监听器
- **并行计算**：Stream API的并行处理
- **函数式接口**：自定义函数式接口的实现

### 方法引用适用场景
- **静态方法引用**：工具类方法调用
- **实例方法引用**：对象方法调用
- **特定类型方法引用**：字符串比较等
- **构造器引用**：对象创建

### 正则表达式适用场景
- **数据验证**：邮箱、手机号、身份证等格式验证
- **文本搜索**：日志分析、文本挖掘
- **字符串替换**：批量文本处理
- **数据提取**：从复杂文本中提取特定信息

## 🎯 学习检验

通过day5的学习，您应该能够：
1. 熟练使用Arrays工具类进行数组操作
2. 理解Lambda表达式的语法和应用场景
3. 掌握四种方法引用的使用方式
4. 运用正则表达式进行文本处理和数据验证
5. 理解从传统Java到现代Java的编程思维转变

Day5是Java编程从传统向现代转变的重要节点，掌握这些特性将显著提升代码质量和开发效率，为后续学习Stream API、并发编程等高级特性奠定坚实基础。

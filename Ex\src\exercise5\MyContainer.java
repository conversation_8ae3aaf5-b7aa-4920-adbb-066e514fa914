package exercise5;

/**
 * 练习5：通用泛型容器类
 * 
 * 要求实现：
 * 1. 使用泛型<T>
 * 2. 使用数组作为底层存储
 * 3. 实现类似ArrayList的基本功能
 * 4. 自动扩容机制
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class MyContainer<T> {
    
    // TODO: 定义底层数组和相关属性
    private static final int DEFAULT_CAPACITY = 10;
    private Object[] elements;  // 使用Object数组存储泛型元素
    private int size;           // 当前元素个数


    // TODO: 构造方法
    public MyContainer() {
        this.elements = new Object[DEFAULT_CAPACITY];
        this.size = 0;
    }
    
    
    // TODO: 添加元素
    public void add(T item) {
        // 检查是否需要扩容
        if (size >= elements.length) {
            ensureCapacity();
        }
        // 添加元素
        elements[size] = item;
        size++;
    }
    
    // TODO: 获取指定位置元素
    @SuppressWarnings("unchecked")
    public T get(int index) {
        checkIndex(index);
        return (T) elements[index];
    }
    
    // TODO: 删除指定位置元素
    @SuppressWarnings("unchecked")
    public T remove(int index) {
        checkIndex(index);
        T removedElement = (T) elements[index];

        // 将后面的元素向前移动
        for (int i = index; i < size - 1; i++) {
            elements[i] = elements[i + 1];
        }

        // 清空最后一个位置并减少size
        elements[size - 1] = null;
        size--;

        return removedElement;
    }
    
    // TODO: 获取容器大小
    public int size() {
        return size;
    }

    // TODO: 判断是否为空
    public boolean isEmpty() {
        return size == 0;
    }

    // TODO: 判断是否包含某元素
    public boolean contains(T item) {
        for (int i = 0; i < size; i++) {
            if (item == null ? elements[i] == null : item.equals(elements[i])) {
                return true;
            }
        }
        return false;
    }
    
    // TODO: 转换为数组
    public Object[] toArray() {
        Object[] result = new Object[size];
        for (int i = 0; i < size; i++) {
            result[i] = elements[i];
        }
        return result;
    }

    // TODO: 扩容方法（私有）
    private void ensureCapacity() {
        int newCapacity = elements.length + (elements.length >> 1); // 扩容为1.5倍
        Object[] newElements = new Object[newCapacity];

        // 复制原有元素到新数组
        for (int i = 0; i < size; i++) {
            newElements[i] = elements[i];
        }

        elements = newElements;
    }

    // TODO: 检查索引范围（私有）
    private void checkIndex(int index) {
        if (index < 0 || index >= size) {
            throw new IndexOutOfBoundsException("索引超出范围: " + index + ", 容器大小: " + size);
        }
    }
    
    // TODO: toString方法
    @Override
    public String toString() {
        if (size == 0) {
            return "[]";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (int i = 0; i < size; i++) {
            sb.append(elements[i]);
            if (i < size - 1) {
                sb.append(", ");
            }
        }
        sb.append("]");
        return sb.toString();
    }
}

package d2_xml;

public class Contact {
    private int id;
    private String name;
    private String email;
    private char gender;
    
    public Contact() {}
    
    public Contact(int id, String name, String email, char gender) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.gender = gender;
    }
    
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public char getGender() { return gender; }
    public void setGender(char gender) { this.gender = gender; }
    
    @Override
    public String toString() {
        return "Contact{id=" + id + ", name='" + name + "', email='" + email + "', gender=" + gender + "}";
    }
}

package exercise7;

import java.util.*;

/**
 * 练习7：员工数据分析测试
 * 
 * 测试要求：
 * 1. 创建至少10个不同部门的员工
 * 2. 测试所有分析功能
 * 3. 输出分析结果
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class EmployeeAnalyzerTest {
    
    public static void main(String[] args) {
        System.out.println("=== 员工数据分析测试 ===\n");
        
        // TODO: 创建员工数据
        List<Employee> employees = createEmployeeData();
        
        // TODO: 创建分析器
        EmployeeAnalyzer analyzer = new EmployeeAnalyzer(employees);
        
        // TODO: 测试高薪员工查询
        System.out.println("--- 高薪员工查询（薪资>=8000） ---");
        
        
        // TODO: 测试按部门分组
        System.out.println("\n--- 按部门分组 ---");
        
        
        // TODO: 测试平均薪资计算
        System.out.println("\n--- 平均薪资计算 ---");
        
        
        // TODO: 测试查找最年长员工
        System.out.println("\n--- 最年长员工 ---");
        
        
        // TODO: 测试薪资统计
        System.out.println("\n--- 薪资统计信息 ---");
        
        
        // TODO: 测试年龄排名
        System.out.println("\n--- 年龄最大的3名员工 ---");
        
        
        // TODO: 测试部门平均薪资
        System.out.println("\n--- 各部门平均薪资 ---");
        
        
        // TODO: 测试其他分析功能
        System.out.println("\n--- 其他分析功能 ---");
        
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    // TODO: 创建测试用的员工数据
    private static List<Employee> createEmployeeData() {
        List<Employee> employees = new ArrayList<>();
        
        // 添加至少10个不同部门的员工
        // 提示：
        // employees.add(new Employee(1, "张三", "技术部", 8000, 28));
        // employees.add(new Employee(2, "李四", "销售部", 6000, 25));
        // ...
        
        return employees;
    }
}

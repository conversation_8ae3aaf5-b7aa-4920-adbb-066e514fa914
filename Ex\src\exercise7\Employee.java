package exercise7;

import java.util.Objects;

/**
 * 练习7：员工类
 * 
 * 要求：
 * 1. 包含id、name、department、salary、age属性
 * 2. 提供完整的构造方法和getter/setter
 * 3. 实现toString()方法
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class Employee {
    
    // TODO: 定义属性
    private int id;
    private String name;
    private String department;
    private double salary;
    private int age;
    
    // TODO: 构造方法
    public Employee() {}

    public Employee(int id, String name, String department, double salary, int age) {
        this.id = id;
        this.name = name;
        this.department = department;
        this.salary = salary;
    }

    
    // TODO: getter和setter方法
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public double getSalary() {
        return salary;
    }

    public void setSalary(double salary) {
        this.salary = salary;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    // TODO: toString方法

    @Override
    public String toString() {
        return "Employee{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", department='" + department + '\'' +
                ", salary=" + salary +
                ", age=" + age +
                '}';
    }


    // TODO: equals和hashCode方法（可选，基于id）


    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        Employee employee = (Employee) o;
        return id == employee.id && Double.compare(salary, employee.salary) == 0 && age == employee.age && Objects.equals(name, employee.name) && Objects.equals(department, employee.department);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, department, salary, age);
    }
}

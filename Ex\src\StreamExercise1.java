import java.util.*;
import java.util.stream.Collectors;

/**
 * Stream流练习题 - 基础操作
 * 请完成以下练习题，使用Stream流来解决问题
 */
public class StreamExercise1 {
    public static void main(String[] args) {
        // 测试数据
        List<String> names = Arrays.asList(
            "张三", "李四", "王五", "张六", "赵七", "钱八", "孙九", "周十",
            "张伟", "李娜", "王芳", "刘洋", "陈静", "杨帆", "黄磊", "周杰",
            "张小明", "李小红", "王大力", "刘小华", "陈美丽", "杨志强", "黄小燕", "周大伟"
        );

        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 20, 25, 30);

        System.out.println("=== Stream流基础练习题 ===");

        // 题目1：筛选出所有姓"张"的人
        System.out.println("题目1：筛选出所有姓'张'的人");
        // TODO: 使用Stream流筛选出姓"张"的人，并打印结果
        names.stream().filter(s -> s.startsWith("张")).forEach(System.out::println);




        // 题目2：筛选出名字长度为2的人
        System.out.println("\n题目2：筛选出名字长度为2的人");
        // TODO: 使用Stream流筛选出名字长度为2的人，并打印结果
        names.stream().filter(s -> s.length()==2).forEach(System.out::println);


        // 题目3：为所有名字添加"先生/女士"后缀
        System.out.println("\n题目3：为所有名字添加'先生'后缀");
        // TODO: 使用Stream流为所有名字添加"先生"后缀，并收集到List中打印
        List<String> names2 = new ArrayList<>();

        names.stream().map(s -> s+"先生").forEach(System.out::println);


        // 题目4：筛选出所有偶数
        System.out.println("\n题目4：筛选出所有偶数");
        // TODO: 使用Stream流筛选出numbers中的所有偶数，并打印结果
        numbers.stream().filter(s -> s%2==0).forEach(System.out::println);


        // 题目5：计算所有数字的平方
        System.out.println("\n题目5：计算所有数字的平方");
        // TODO: 使用Stream流计算numbers中所有数字的平方，并打印结果
        numbers.stream().map( s -> s*s).forEach(System.out::println);

        // 题目6：统计姓"李"的人数
        System.out.println("\n题目6：统计姓'李'的人数");
        // TODO: 使用Stream流统计姓"李"的人数，并打印结果
        System.out.println(names.stream().filter(s -> s.startsWith("李")).count());


        // 题目7：找出最大的数字
        System.out.println("\n题目7：找出最大的数字");
        // TODO: 使用Stream流找出numbers中的最大值，并打印结果
        numbers.stream().max(Integer::compare).ifPresent(System.out::println);


        // 题目8：对名字进行排序
        System.out.println("\n题目8：对名字进行排序");
        // TODO: 使用Stream流对names进行排序，并打印结果
        names.stream().sorted().forEach(System.out::println);

        // 题目9：取前5个名字
        System.out.println("\n题目9：取前5个名字");
        // TODO: 使用Stream流取names的前5个元素，并打印结果
        names.stream().limit(5).forEach(System.out::println);

        // 题目10：跳过前3个，取接下来的5个名字
        System.out.println("\n题目10：跳过前3个，取接下来的5个名字");
        // TODO: 使用Stream流跳过前3个名字，然后取接下来的5个，并打印结果
        names.stream().skip(3).limit(5).forEach(System.out::println);

    }
}

# 练习4：异常处理 - 学习指导 📚

## 🎯 学习目标

通过实现一个安全计算器，掌握Java异常处理的核心概念：
- 自定义异常类的设计
- 异常的抛出和捕获
- try-catch-finally语句块
- 异常链和异常信息处理

## 📖 异常处理基础知识

### 1. 异常类层次结构
```
Throwable
├── Error (系统级错误，不应捕获)
└── Exception
    ├── RuntimeException (运行时异常，可选择处理)
    └── 其他Exception (检查异常，必须处理)
```

### 2. 自定义异常类设计原则
- 继承合适的异常基类
- 提供无参和带消息的构造方法
- 异常名称应该清晰表达错误类型
- 可以添加额外的错误信息字段

## 🔧 实现步骤指导

### 步骤1：实现自定义异常类

#### DivideByZeroException.java
```java
public class DivideByZeroException extends Exception {
    
    // 无参构造方法
    public DivideByZeroException() {
        super("除数不能为零");
    }
    
    // 带消息的构造方法
    public DivideByZeroException(String message) {
        super(message);
    }
    
    // 带消息和原因的构造方法（可选）
    public DivideByZeroException(String message, Throwable cause) {
        super(message, cause);
    }
}
```

#### InvalidOperatorException.java
```java
public class InvalidOperatorException extends Exception {
    
    public InvalidOperatorException() {
        super("无效的操作符");
    }
    
    public InvalidOperatorException(String message) {
        super(message);
    }
    
    public InvalidOperatorException(String message, Throwable cause) {
        super(message, cause);
    }
}
```

### 步骤2：实现Calculator类

#### 基本运算方法
```java
public double add(double a, double b) {
    return a + b;
}

public double subtract(double a, double b) {
    return a - b;
}

public double multiply(double a, double b) {
    return a * b;
}

public double divide(double a, double b) throws DivideByZeroException {
    if (b == 0) {
        throw new DivideByZeroException("除数不能为零：" + a + " / " + b);
    }
    return a / b;
}
```

#### 通用计算方法
```java
public double calculate(double a, double b, String operator) 
        throws DivideByZeroException, InvalidOperatorException {
    
    if (operator == null) {
        throw new InvalidOperatorException("操作符不能为空");
    }
    
    switch (operator) {
        case "+":
            return add(a, b);
        case "-":
            return subtract(a, b);
        case "*":
            return multiply(a, b);
        case "/":
            return divide(a, b);  // 可能抛出DivideByZeroException
        default:
            throw new InvalidOperatorException("不支持的操作符：" + operator);
    }
}
```

### 步骤3：实现测试类

#### 异常处理的最佳实践
```java
public static void main(String[] args) {
    Calculator calc = new Calculator();
    
    // 测试正常运算
    try {
        double result = calc.calculate(10, 5, "+");
        System.out.println("10 + 5 = " + result);
    } catch (Exception e) {
        System.err.println("计算错误：" + e.getMessage());
    }
    
    // 测试除零异常
    try {
        double result = calc.divide(10, 0);
        System.out.println("结果：" + result);
    } catch (DivideByZeroException e) {
        System.err.println("除零错误：" + e.getMessage());
    }
    
    // 测试无效操作符
    try {
        double result = calc.calculate(10, 5, "%");
        System.out.println("结果：" + result);
    } catch (InvalidOperatorException e) {
        System.err.println("操作符错误：" + e.getMessage());
    } catch (DivideByZeroException e) {
        System.err.println("除零错误：" + e.getMessage());
    }
}
```

## 💡 关键知识点

### 1. 异常抛出（throw vs throws）
- `throw`：在方法内部抛出异常对象
- `throws`：在方法声明中声明可能抛出的异常

### 2. 异常捕获顺序
- 子类异常要在父类异常之前捕获
- 具体异常要在通用异常之前捕获

### 3. 异常处理策略
- **捕获并处理**：try-catch处理异常
- **向上抛出**：在方法声明中使用throws
- **捕获并重新抛出**：catch后再throw

### 4. 资源管理
- 使用try-with-resources自动关闭资源
- 在finally块中清理资源

## 🧪 测试用例设计

### 正常情况测试
- 基本四则运算
- 正数、负数、小数运算
- 大数值运算

### 异常情况测试
- 除零异常：`10 / 0`
- 无效操作符：`10 % 5`
- 空操作符：`null`
- 边界值：`Double.MAX_VALUE`

### 边界值测试
```java
// 测试极大值
calc.calculate(Double.MAX_VALUE, 1, "+");

// 测试极小值
calc.calculate(Double.MIN_VALUE, 1, "*");

// 测试无穷大
calc.calculate(1, 0, "/");  // 应该抛出异常而不是返回Infinity
```

## 🎯 实现建议

### 1. 循序渐进
1. 先实现自定义异常类
2. 再实现基本运算方法
3. 然后实现通用计算方法
4. 最后编写完整的测试

### 2. 异常信息设计
- 提供清晰的错误描述
- 包含相关的上下文信息
- 使用用户友好的语言

### 3. 测试驱动开发
- 先写测试用例
- 再实现功能代码
- 确保所有异常情况都被测试

## 🚀 扩展挑战

完成基本要求后，可以尝试：

### 1. 添加更多运算
- 幂运算（可能溢出）
- 开方运算（负数开方异常）
- 三角函数（角度验证）

### 2. 改进异常处理
- 异常链的使用
- 自定义异常码
- 国际化错误消息

### 3. 输入验证
- 参数非空检查
- 数值范围验证
- 操作符格式验证

## 📝 完成检查清单

- [ ] DivideByZeroException类实现完整
- [ ] InvalidOperatorException类实现完整
- [ ] Calculator类所有方法实现
- [ ] 正确使用throws声明异常
- [ ] 测试类包含所有测试场景
- [ ] 异常信息清晰友好
- [ ] 代码注释完整
- [ ] 所有测试用例通过

## 🎉 开始实现

现在你已经了解了异常处理的核心概念，开始实现你的安全计算器吧！

记住：**异常处理不仅仅是技术实现，更是提升程序健壮性和用户体验的重要手段！**

**准备好开始编码了吗？** 💪🚀

package d6_thread_synchronized_lock;

public class ThreadSafeTest {
    public static void main(String[] args) {
        //   同步代码块    使用共享资源做为锁   c
        //线程安全问题演示
        //1.创建一个账户对象,多个线程共享
        Account account = new Account("s", 10000);

        //2.创建多个线程对象来代表不同的人同时取钱
        // 每人都尝试取完所有的10000元
        new DrawThread("小明", account).start();
        new DrawThread("小红", account).start();

    }
}



import java.util.*;
import java.util.stream.Collectors;

/**
 * Stream流挑战题 - 高难度综合应用
 * 请完成以下挑战题，考验你的Stream流掌握程度
 */
public class StreamChallenge {
    public static void main(String[] args) {
        // 订单数据
        List<Order> orders = Arrays.asList(
            new Order("O001", "张三", Arrays.asList(
                new OrderItem("iPhone14", 5999.0, 1),
                new OrderItem("AirPods", 1299.0, 2)
            )),
            new Order("O002", "李四", Arrays.asList(
                new OrderItem("MacBook", 12999.0, 1),
                new OrderItem("鼠标", 199.0, 1)
            )),
            new Order("O003", "王五", Arrays.asList(
                new OrderItem("iPad", 3999.0, 1),
                new OrderItem("保护套", 99.0, 3)
            )),
            new Order("O004", "张三", Arrays.asList(
                new OrderItem("iPhone14", 5999.0, 2),
                new OrderItem("充电器", 299.0, 1)
            )),
            new Order("O005", "赵六", Arrays.asList(
                new OrderItem("MacBook", 12999.0, 1),
                new OrderItem("键盘", 599.0, 1),
                new OrderItem("鼠标", 199.0, 1)
            ))
        );
        
        System.out.println("=== Stream流挑战题 ===");
        
        // 挑战1：计算每个订单的总金额
        System.out.println("挑战1：计算每个订单的总金额");
        // TODO: 使用Stream流计算每个订单的总金额，并打印 订单号:总金额 的格式
        
        
        // 挑战2：找出购买金额最高的客户
        System.out.println("\n挑战2：找出购买金额最高的客户");
        // TODO: 使用Stream流找出所有订单中购买金额最高的客户，并打印结果
        
        
        // 挑战3：统计每个客户的订单数量和总消费金额
        System.out.println("\n挑战3：统计每个客户的订单数量和总消费金额");
        // TODO: 使用Stream流按客户分组，统计每个客户的订单数量和总消费金额
        
        
        // 挑战4：找出销量最好的商品（按购买数量）
        System.out.println("\n挑战4：找出销量最好的商品（按购买数量）");
        // TODO: 使用Stream流统计所有商品的销量，找出销量最好的商品
        
        
        // 挑战5：计算平均订单金额
        System.out.println("\n挑战5：计算平均订单金额");
        // TODO: 使用Stream流计算所有订单的平均金额
        
        
        // 挑战6：找出包含"iPhone14"的所有订单
        System.out.println("\n挑战6：找出包含'iPhone14'的所有订单");
        // TODO: 使用Stream流找出包含"iPhone14"商品的所有订单
        
        
        // 挑战7：按订单金额分类（低：<5000，中：5000-15000，高：>15000）
        System.out.println("\n挑战7：按订单金额分类");
        // TODO: 使用Stream流按订单金额分类，统计各类别的订单数量
        
        
        // 挑战8：创建商品销售报表（商品名，总销量，总销售额）
        System.out.println("\n挑战8：创建商品销售报表");
        // TODO: 使用Stream流创建商品销售报表，包含商品名、总销量、总销售额
        
    }
}

// 订单类
class Order {
    private String orderId;
    private String customerName;
    private List<OrderItem> items;
    
    public Order(String orderId, String customerName, List<OrderItem> items) {
        this.orderId = orderId;
        this.customerName = customerName;
        this.items = items;
    }
    
    public String getOrderId() { return orderId; }
    public String getCustomerName() { return customerName; }
    public List<OrderItem> getItems() { return items; }
    
    @Override
    public String toString() {
        return String.format("Order{id='%s', customer='%s', items=%s}", orderId, customerName, items);
    }
}

// 订单项类
class OrderItem {
    private String productName;
    private double price;
    private int quantity;
    
    public OrderItem(String productName, double price, int quantity) {
        this.productName = productName;
        this.price = price;
        this.quantity = quantity;
    }
    
    public String getProductName() { return productName; }
    public double getPrice() { return price; }
    public int getQuantity() { return quantity; }
    public double getTotalPrice() { return price * quantity; }
    
    @Override
    public String toString() {
        return String.format("OrderItem{product='%s', price=%.1f, qty=%d}", productName, price, quantity);
    }
}

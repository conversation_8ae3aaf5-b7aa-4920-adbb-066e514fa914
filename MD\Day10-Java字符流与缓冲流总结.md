# Day10 Java字符流与缓冲流总结

## 📚 学习目标

通过day10-io-code的学习，掌握Java IO流的进阶特性：
1. **字符输入流** - FileReader解决中文乱码问题
2. **字符输出流** - FileWriter处理文本文件写入
3. **缓冲流** - 提升IO操作性能的关键技术
4. **流的包装** - 装饰器模式在IO中的应用
5. **性能优化** - 字符流与缓冲流的性能对比

## 📖 字符流 - 解决中文乱码的利器

### 核心概念
- 字符流是专门用于处理文本文件的IO流
- 以字符为单位进行读写操作，而不是字节
- 自动处理字符编码转换，解决中文乱码问题
- 主要类：Reader（输入）、Writer（输出）

### 字符流 vs 字节流对比

| 特性 | 字符流 | 字节流 |
|------|--------|--------|
| 处理单位 | 字符（char） | 字节（byte） |
| 适用文件 | 文本文件 | 所有文件类型 |
| 中文处理 | 无乱码 | 可能乱码 |
| 性能 | 文本处理较优 | 二进制文件较优 |
| 典型应用 | 配置文件、日志 | 图片、音视频复制 |

## 📥 字符输入流 - FileReader

### 核心概念
- FileReader是文件字符输入流，继承自Reader抽象类
- 专门用于读取文本文件，自动处理字符编码
- 读取方式：单字符读取、字符数组读取
- 默认使用系统默认字符编码（通常是UTF-8）

### 1. 单字符读取 (FileReaderDemo1)

```java
public class FileReaderDemo1 {
    public static void main(String[] args) throws Exception {
        // 1. 创建字符输入流与源文件接通
        Reader fr = new FileReader("day10-io-code/src/dei01.txt");
        
        // 2. 循环读取每个字符
        int c; // 存储读取的字符
        while((c = fr.read()) != -1) {
            System.out.print((char)c);
        }
        
        // 3. 关闭流
        fr.close();
    }
}
```

**测试文件内容**：`dei01.txt` 包含 "a爱"

**运行结果**：
```
a爱
```

**核心特点**：
- **无乱码**：中文字符"爱"正确显示，不会出现乱码
- **字符单位**：read()方法返回的是字符的Unicode值
- **简单易用**：API与字节流类似，但处理的是字符

### 2. 字符数组读取 (FileReaderDemo2)

```java
public class FileReaderDemo2 {
    public static void main(String[] args) throws Exception {
        try(Reader fr = new FileReader("day10-io-code/src/dei02.txt")) {
            // 定义字符数组作为缓冲区
            char[] buffer = new char[3];
            int len; // 实际读取的字符数
            
            while((len = fr.read(buffer)) > 0) {
                String rs = new String(buffer, 0, len);
                System.out.print(rs);
            }
            
        } catch(Exception e) {
            e.printStackTrace();
        }
    }
}
```

**测试文件内容**：`dei02.txt` 包含 "af爱爱"

**运行结果**：
```
af爱爱
```

**优势分析**：
- **性能提升**：批量读取比单字符读取效率更高
- **无乱码**：字符数组确保中文字符完整读取
- **资源管理**：使用try-with-resources自动管理资源
- **最佳方案**：这是目前学过的读取文本内容最好的方案

## 📤 字符输出流 - FileWriter

### 核心概念
- FileWriter是文件字符输出流，继承自Writer抽象类
- 专门用于写入文本文件，自动处理字符编码
- 写入方式：单字符、字符数组、字符串
- 支持追加模式和覆盖模式

### 1. 字符输出流操作 (FileWriteDemo3)

```java
public class FileWriteDemo3 {
    public static void main(String[] args) throws Exception {
        try(Writer fw = new FileWriter("day10-io-code/src/dei03.txt", true)) {
            // 1. 写入单个字符
            fw.write(97);        // 写入ASCII码对应的字符 'a'
            fw.write('一');      // 直接写入中文字符
            fw.write("\r\n");    // 写入换行符
            
            // 2. 重复写入测试
            fw.write(97);
            fw.write('一');
            
            // 3. 写入字符串的部分内容
            // 从索引3开始，写入3个字符
            fw.write("更加是哪里喁喁", 3, 3);
            
            // 4. 写入字符数组
            char[] chars = "abc我爱你中国666".toCharArray();
            fw.write(chars);
            
            // 5. 刷新缓冲区
            fw.flush();
            
        } catch(Exception e) {
            e.printStackTrace();
        }
    }
}
```

**运行结果**：文件`dei03.txt`内容为：
```
a一
a一哪里喁abc我爱你中国666
```

**核心方法**：
- **write(int c)**：写入单个字符
- **write(char[] chars)**：写入字符数组
- **write(String str)**：写入字符串
- **write(String str, int off, int len)**：写入字符串的指定部分
- **flush()**：刷新缓冲区到磁盘

**写入模式**：
- **覆盖模式**：`new FileWriter("file.txt")`
- **追加模式**：`new FileWriter("file.txt", true)`

## 🚀 缓冲流 - 性能优化的关键

### 核心概念
- 缓冲流是对基础流的包装，提供内部缓冲区
- 减少实际的磁盘IO次数，显著提升性能
- 采用装饰器设计模式，不改变原有流的功能
- 主要类：BufferedInputStream、BufferedOutputStream、BufferedReader、BufferedWriter

### 缓冲流工作原理

```
普通流：应用程序 ←→ 磁盘文件
        每次读写都直接访问磁盘

缓冲流：应用程序 ←→ 内存缓冲区 ←→ 磁盘文件
        批量读写，减少磁盘访问次数
```

### 1. 缓冲字节流 (BufferedInputStreamDemo1)

```java
public class BufferedInputStreamDemo1 {
    public static void main(String[] args) {
        try(
            // 创建基础字节输入流
            InputStream is = new FileInputStream("source.png");
            // 使用缓冲流包装基础流
            InputStream bis = new BufferedInputStream(is);
            
            // 创建基础字节输出流
            OutputStream os = new FileOutputStream("target.png");
            // 使用缓冲流包装基础流
            OutputStream bos = new BufferedOutputStream(os);
        ) {
            byte[] buffer = new byte[1024]; // 1KB缓冲区
            int len;
            
            while((len = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

**缓冲流特点**：
- **双重缓冲**：应用程序缓冲区 + 流内部缓冲区
- **自动管理**：缓冲区的填充和刷新由流自动管理
- **透明使用**：API与基础流完全相同
- **性能提升**：大文件复制性能提升明显

## 📊 性能对比分析

### 不同IO方式性能对比（复制100MB文件）

| IO方式 | 耗时 | 性能等级 | 适用场景 |
|--------|------|----------|----------|
| 字节流单字节读取 | ~30秒 | ⭐ | 不推荐 |
| 字节流数组读取 | ~0.5秒 | ⭐⭐⭐ | 一般文件 |
| 缓冲字节流 | ~0.1秒 | ⭐⭐⭐⭐⭐ | 大文件推荐 |
| 字符流单字符读取 | ~25秒 | ⭐ | 不推荐 |
| 字符流数组读取 | ~0.4秒 | ⭐⭐⭐ | 文本文件 |
| 缓冲字符流 | ~0.08秒 | ⭐⭐⭐⭐⭐ | 大文本文件 |

### 缓冲区大小对性能的影响

```java
// 不同缓冲区大小的性能测试
BufferedInputStream bis1 = new BufferedInputStream(is, 1024);    // 1KB
BufferedInputStream bis2 = new BufferedInputStream(is, 8192);    // 8KB（默认）
BufferedInputStream bis3 = new BufferedInputStream(is, 65536);   // 64KB
```

**最佳实践**：
- **默认大小**：8KB（8192字节）适合大多数场景
- **大文件**：可以适当增加到64KB或更大
- **小文件**：默认大小即可，过大反而浪费内存

## 🔧 流的选择策略

### 1. 根据文件类型选择

```java
// 文本文件处理
try(BufferedReader br = new BufferedReader(new FileReader("config.txt"));
    BufferedWriter bw = new BufferedWriter(new FileWriter("output.txt"))) {
    // 处理文本内容
}

// 二进制文件处理
try(BufferedInputStream bis = new BufferedInputStream(new FileInputStream("image.jpg"));
    BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream("copy.jpg"))) {
    // 处理二进制内容
}
```

### 2. 根据性能需求选择

```java
// 高性能要求：使用缓冲流
BufferedInputStream bis = new BufferedInputStream(new FileInputStream("large.dat"));

// 简单操作：直接使用基础流
FileInputStream fis = new FileInputStream("small.txt");

// 文本处理：优先选择字符流
FileReader fr = new FileReader("text.txt");
```

## ⚠️ 常见问题与解决方案

### 1. 字符编码问题
**问题**：FileReader使用系统默认编码，可能不匹配文件编码
```java
// 可能出现编码问题
FileReader fr = new FileReader("gbk_file.txt"); // 文件是GBK编码，系统是UTF-8
```

**解决方案**：使用InputStreamReader指定编码
```java
// 指定编码读取
InputStreamReader isr = new InputStreamReader(
    new FileInputStream("gbk_file.txt"), "GBK");
BufferedReader br = new BufferedReader(isr);
```

### 2. 缓冲区刷新问题
**问题**：写入的数据可能停留在缓冲区中
```java
BufferedWriter bw = new BufferedWriter(new FileWriter("output.txt"));
bw.write("重要数据");
// 如果程序异常退出，数据可能丢失
```

**解决方案**：及时刷新缓冲区
```java
BufferedWriter bw = new BufferedWriter(new FileWriter("output.txt"));
bw.write("重要数据");
bw.flush(); // 强制刷新到磁盘
// 或者使用try-with-resources自动关闭
```

### 3. 流包装层次过多
**问题**：过度包装导致代码复杂
```java
// 过度包装
BufferedReader br = new BufferedReader(
    new InputStreamReader(
        new BufferedInputStream(
            new FileInputStream("file.txt")), "UTF-8"));
```

**解决方案**：合理选择包装层次
```java
// 简化包装
BufferedReader br = new BufferedReader(
    new InputStreamReader(new FileInputStream("file.txt"), "UTF-8"));
```

## 🎯 IO流体系总结

### Java IO流分类体系

```
IO流
├── 字节流（Stream）
│   ├── 输入流（InputStream）
│   │   ├── FileInputStream（文件字节输入）
│   │   └── BufferedInputStream（缓冲字节输入）
│   └── 输出流（OutputStream）
│       ├── FileOutputStream（文件字节输出）
│       └── BufferedOutputStream（缓冲字节输出）
└── 字符流（Reader/Writer）
    ├── 输入流（Reader）
    │   ├── FileReader（文件字符输入）
    │   ├── InputStreamReader（字节转字符输入）
    │   └── BufferedReader（缓冲字符输入）
    └── 输出流（Writer）
        ├── FileWriter（文件字符输出）
        ├── OutputStreamWriter（字符转字节输出）
        └── BufferedWriter（缓冲字符输出）
```

### 流的选择决策树

```
开始
  ↓
处理什么类型的文件？
  ├── 文本文件 → 使用字符流（Reader/Writer）
  │   ↓
  │   需要高性能？
  │   ├── 是 → BufferedReader/BufferedWriter
  │   └── 否 → FileReader/FileWriter
  │
  └── 二进制文件 → 使用字节流（InputStream/OutputStream）
      ↓
      需要高性能？
      ├── 是 → BufferedInputStream/BufferedOutputStream
      └── 否 → FileInputStream/FileOutputStream
```

## 🛠️ 最佳实践指南

### 1. 文本文件处理模板

```java
// 读取文本文件
public static String readTextFile(String filePath) {
    StringBuilder content = new StringBuilder();
    try(BufferedReader br = new BufferedReader(new FileReader(filePath))) {
        String line;
        while((line = br.readLine()) != null) {
            content.append(line).append("\n");
        }
    } catch(IOException e) {
        throw new RuntimeException("读取文件失败: " + filePath, e);
    }
    return content.toString();
}

// 写入文本文件
public static void writeTextFile(String filePath, String content) {
    try(BufferedWriter bw = new BufferedWriter(new FileWriter(filePath))) {
        bw.write(content);
        bw.flush();
    } catch(IOException e) {
        throw new RuntimeException("写入文件失败: " + filePath, e);
    }
}
```

### 2. 二进制文件复制模板

```java
public static void copyBinaryFile(String sourcePath, String targetPath) {
    try(BufferedInputStream bis = new BufferedInputStream(new FileInputStream(sourcePath));
        BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(targetPath))) {

        byte[] buffer = new byte[8192]; // 8KB缓冲区
        int len;
        while((len = bis.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }

    } catch(IOException e) {
        throw new RuntimeException("文件复制失败: " + sourcePath + " -> " + targetPath, e);
    }
}
```

### 3. 编码安全的文本处理

```java
public static String readTextFileWithEncoding(String filePath, String encoding) {
    StringBuilder content = new StringBuilder();
    try(BufferedReader br = new BufferedReader(
            new InputStreamReader(new FileInputStream(filePath), encoding))) {

        String line;
        while((line = br.readLine()) != null) {
            content.append(line).append("\n");
        }

    } catch(IOException e) {
        throw new RuntimeException("读取文件失败: " + filePath, e);
    }
    return content.toString();
}
```

## 📈 性能优化技巧

### 1. 缓冲区大小调优

```java
// 根据文件大小选择缓冲区
public static int getOptimalBufferSize(long fileSize) {
    if (fileSize < 1024 * 1024) {          // < 1MB
        return 1024;                        // 1KB
    } else if (fileSize < 10 * 1024 * 1024) { // < 10MB
        return 8192;                        // 8KB
    } else {                                // >= 10MB
        return 65536;                       // 64KB
    }
}
```

### 2. 批量操作优化

```java
// 批量写入优化
public static void writeLargeText(String filePath, List<String> lines) {
    try(BufferedWriter bw = new BufferedWriter(new FileWriter(filePath), 65536)) {
        for(String line : lines) {
            bw.write(line);
            bw.newLine();

            // 每1000行刷新一次，平衡性能和安全性
            if(lines.indexOf(line) % 1000 == 0) {
                bw.flush();
            }
        }
    } catch(IOException e) {
        throw new RuntimeException("批量写入失败", e);
    }
}
```

### 3. 内存使用优化

```java
// 大文件逐行处理，避免内存溢出
public static void processLargeFile(String filePath, Consumer<String> lineProcessor) {
    try(BufferedReader br = new BufferedReader(new FileReader(filePath))) {
        String line;
        while((line = br.readLine()) != null) {
            lineProcessor.accept(line); // 逐行处理，不占用大量内存
        }
    } catch(IOException e) {
        throw new RuntimeException("处理大文件失败", e);
    }
}
```

## 🎯 学习总结

### 核心知识点
1. **字符流优势**：专门处理文本文件，解决中文乱码问题
2. **FileReader/FileWriter**：基础字符流，简单易用
3. **缓冲流机制**：通过内部缓冲区显著提升IO性能
4. **装饰器模式**：缓冲流包装基础流，增强功能不改变接口
5. **性能优化**：合理选择流类型和缓冲区大小

### 字符流特点
- **编码处理**：自动处理字符编码转换
- **文本专用**：专门用于文本文件处理
- **API简洁**：与字节流API类似，学习成本低
- **性能良好**：配合缓冲流性能优异

### 缓冲流优势
- **性能提升**：减少系统调用次数，提升IO效率
- **透明使用**：API与基础流相同，使用简单
- **内存管理**：自动管理内部缓冲区
- **适配性强**：可以包装任何基础流

### 选择原则
- **文本文件**：优先选择字符流（Reader/Writer）
- **二进制文件**：必须使用字节流（InputStream/OutputStream）
- **性能要求高**：使用缓冲流包装基础流
- **编码敏感**：使用InputStreamReader/OutputStreamWriter指定编码

### 最佳实践
- **资源管理**：始终使用try-with-resources
- **异常处理**：合理处理IOException
- **性能调优**：根据文件大小选择合适的缓冲区
- **编码规范**：明确指定字符编码，避免平台依赖

Day10在Day09字节流基础上引入了字符流和缓冲流，完善了Java IO体系的核心内容，为处理不同类型的文件提供了最优解决方案，显著提升了IO操作的性能和可靠性。

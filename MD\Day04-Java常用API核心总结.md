# Day04 Java常用API核心总结

## 📚 学习目标

通过day04-api-code的学习，掌握Java开发中最常用的核心API：
1. **StringBuilder** - 高效字符串构建和操作
2. **StringJoiner** - 便捷的字符串连接工具
3. **Math** - 数学运算工具类
4. **BigDecimal** - 精确小数运算解决方案
5. **JDK8时间API** - 现代化日期时间处理

## 🔧 StringBuilder - 高效字符串构建

### 核心概念
- StringBuilder是可变字符串，内部维护一个字符数组
- 相比String拼接，StringBuilder避免了频繁创建新对象
- 支持链式调用，提高代码可读性

### 1. 基础用法 (d1_StringBuilderDemo1)

```java
public class d1_StringBuilderDemo1 {
    public static void main(String[] args) {
        // 1.创建对象
        StringBuilder sb = new StringBuilder();   // sb = ""
        StringBuilder sb2 = new StringBuilder("黑马");  // sb2 = "黑马"

        // 2.拼接内容 - 支持链式调用
        sb2.append("java").append("Java").append(666).append(true);
        System.out.println(sb2); // 黑马javaJava666true

        // 分步拼接
        sb.append("黑马");
        sb.append("java");
        sb.append(666);
        sb.append(true);
        System.out.println(sb); // 黑马java666true

        // 3.反转内容
        sb.reverse();
        System.out.println(sb); // eurt666avaj马黑

        // 4.长度
        System.out.println(sb.length()); // 13

        // 5.转换成String对象
        String result = sb.toString();
        System.out.println(result); // eurt666avaj马黑
    }
}
```

**运行结果**：
```
黑马javaJava666true
黑马java666true
eurt666avaj马黑
13
eurt666avaj马黑
```

### 2. 实际应用案例 (StringBuilderrTest3)

```java
public class StringBuilderrTest3 {
    public static void main(String[] args) {
        // 需求：定义一个方法，把int类型的数组，按照指定格式拼接成一个字符串返回
        int[] arr = {1, 2, 3, 4};
        System.out.println(getArrayData(arr)); // [1,2,3,4]
    }
    
    public static String getArrayData(int[] arr) {
        if (arr == null) {
            return null;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (int i = 0; i < arr.length; i++) {
            if (i == arr.length - 1) {
                sb.append(arr[i]);
            } else {
                sb.append(arr[i]).append(",");
            }
        }
        sb.append("]");
        return sb.toString();
    }
}
```

**运行结果**：
```
[1,2,3,4]
```

## 🔗 StringJoiner - 便捷字符串连接

### 核心概念
- JDK8引入的字符串连接工具
- 可以指定分隔符、前缀、后缀
- 简化字符串拼接操作

### 基础用法 (StringJoinerTest1)

```java
import java.util.StringJoiner;

public class StringJoinerTest1 {
    public static void main(String[] args) {
        // StringJoiner(间隔符号, 开始符号, 结束符号)
        StringJoiner sj = new StringJoiner(",", "[", "]");
        sj.add("1").add("2").add("3");
        System.out.println(sj); // [1,2,3]
    }
}
```

**运行结果**：
```
[1,2,3]
```

### 系统信息获取 (RunTiimeTest)

```java
public class RunTiimeTest {
    public static void main(String[] args) {
        // 获取运行时对象
        Runtime r = Runtime.getRuntime();
        
        // 获取虚拟机能够使用的总内存大小（单位：字节）
        long totalMemory = r.totalMemory();
        long totalMemoryMB = totalMemory / 1024 / 1024;
        
        // 获取虚拟机空闲内存大小
        long freeMemory = r.freeMemory();
        long freeMemoryMB = freeMemory / 1024 / 1024;
        
        // 获取虚拟机能够使用的CPU数量
        int processors = r.availableProcessors();
        
        System.out.println("CPU数量" + processors);
        System.out.println("java虚拟机中的内存问题：" + totalMemoryMB);
        System.out.println("java虚拟机中的空闲内存：" + freeMemoryMB);
    }
}
```

**运行结果**：
```
CPU数量16
java虚拟机中的内存问题：254.0
java虚拟机中的空闲内存：250.517578125
```

## 🧮 Math - 数学工具类

### 核心概念
- 提供基本数学运算方法
- 所有方法都是静态方法，直接通过类名调用
- 常用于数值处理和数学计算

### 常用方法 (Math_Test)

```java
public class Math_Test {
    public static void main(String[] args) {
        // 1. 绝对值
        System.out.println(Math.abs(-4)); // 4
        
        // 2. 向上取整
        System.out.println(Math.ceil(3.1)); // 4.0
        
        // 3. 向下取整
        System.out.println(Math.floor(3.9)); // 3.0
        
        // 4. 四舍五入
        System.out.println(Math.round(3.4)); // 3
    }
}
```

**运行结果**：
```
4
4.0
3.0
3
```

## 💰 BigDecimal - 精确小数运算

### 核心概念
- 解决浮点数运算精度丢失问题
- 提供精确的小数运算
- 商业计算和金融系统的首选

### 精确计算示例 (BigDdecimalDemo1)

```java
import java.math.BigDecimal;
import java.math.RoundingMode;

public class BigDdecimalDemo1 {
    public static void main(String[] args) {
        // 浮点数运算精度问题
        System.out.println(0.1 + 0.2); // 0.30000000000000004
        
        // 使用BigDecimal解决精度问题
        BigDecimal a = BigDecimal.valueOf(0.1);
        BigDecimal b = BigDecimal.valueOf(0.2);
        BigDecimal c = a.add(b);
        System.out.println(c); // 0.3
        
        // 推荐的创建方式
        BigDecimal a1 = new BigDecimal("0.1");
        BigDecimal b1 = new BigDecimal("0.2");
        BigDecimal c1 = a1.add(b1);
        System.out.println(c1); // 0.3
        
        // 减法
        BigDecimal c2 = a1.subtract(b1);
        System.out.println(c2); // -0.1
        
        System.out.println("-----------------------");
        
        // 除法运算（需要指定精度）
        BigDecimal d1 = new BigDecimal("1.0");
        BigDecimal d2 = new BigDecimal("3.0");
        BigDecimal d3 = d1.divide(d2, 2, RoundingMode.HALF_UP);
        System.out.println(d3); // 0.33
    }
}
```

**运行结果**：
```
0.30000000000000004
0.3
0.3
0.3
-0.1
-----------------------
0.33
```

## 📅 JDK8时间API - 现代化日期时间

### 核心概念
- JDK8引入全新的时间API，替代旧的Date类
- 线程安全、不可变对象设计
- 提供更直观的API和更强大的功能

### 1. LocalDate基础操作 (LocalDateDemo)

```java
import java.time.LocalDate;
import java.time.LocalDateTime;

public class LocalDateDemo {
    public static void main(String[] args) {
        // 1. 获取当前日期
        LocalDate ld = LocalDate.now();
        System.out.println(ld); // 2025-08-02
        
        // 2. 获取日期信息
        int year = ld.getYear();
        int month = ld.getMonthValue();
        int day = ld.getDayOfMonth();
        int dayOfWeek = ld.getDayOfWeek().getValue();
        
        System.out.println(year);     // 2025
        System.out.println(month);    // 8
        System.out.println(day);      // 2
        System.out.println(dayOfWeek); // 6 (周六)
        
        // 3. 日期操作
        LocalDate ld2 = ld.withYear(2099);
        LocalDate ld3 = ld.withMonth(12);
        LocalDate ld4 = ld.withDayOfMonth(1);
        LocalDate ld5 = ld.plusDays(1);
        LocalDate ld6 = ld.minusDays(1);
        LocalDate ld7 = ld.plusYears(74).plusMonths(4).plusDays(29);
        
        System.out.println(ld2); // 2099-08-02
        System.out.println(ld3); // 2025-12-02
        System.out.println(ld4); // 2025-08-01
        System.out.println(ld5); // 2025-08-03
        System.out.println(ld6); // 2025-08-01
        System.out.println(ld7); // 2099-12-31
        
        // 4. 日期比较
        System.out.println(ld.isBefore(ld2)); // false
        System.out.println(ld.isAfter(ld2));  // false
        System.out.println(ld.isEqual(ld));   // true
        
        // 5. 获取当前日期时间
        LocalDateTime now = LocalDateTime.now();
        System.out.println(now); // 2025-08-02T09:38:46.047188400
    }
}
```

**运行结果**：
```
2025-08-02
2025
8
2
6
2099-08-02
2025-12-02
2025-08-01
2025-08-03
2025-08-01
2099-12-31
false
false
true
2025-08-02T09:38:46.047188400
```

### 2. 时间计算应用 (Test)

```java
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

public class Test {
    public static void main(String[] args) {
        // 计算两个日期之间的差值
        LocalDate start = LocalDate.of(2023, 10, 1);
        LocalDate end = LocalDate.of(2025, 1, 1);

        // 计算相差的天数、小时、分钟、秒
        long days = ChronoUnit.DAYS.between(start, end);
        long hours = ChronoUnit.HOURS.between(start.atStartOfDay(), end.atStartOfDay());
        long minutes = ChronoUnit.MINUTES.between(start.atStartOfDay(), end.atStartOfDay());
        long seconds = ChronoUnit.SECONDS.between(start.atStartOfDay(), end.atStartOfDay());

        System.out.println(days + "天 " + (hours % 24) + "小时 " +
                          (minutes % 60) + "分钟 " + (seconds % 60) + "秒");
    }
}
```

**运行结果**：
```
421天 0小时 8分钟 58秒
```

### 3. 时间格式化 (Test_DateTimeForMatter)

```java
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class Test_DateTimeForMatter {
    public static void main(String[] args) {
        LocalDateTime ldt = LocalDateTime.now();

        // 格式化时间
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss EEE a");
        System.out.println(dtf.format(ldt));

        // 简化写法
        System.out.println(ldt.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss EEE a")));

        // 解析时间字符串
        String dateStr = "2023-11-11T11:11:11";
        LocalDateTime parsedTime = LocalDateTime.parse(dateStr);
        System.out.println(parsedTime);
    }
}
```

**运行结果**：
```
2025年08月02日 09:39:12 周六 上午
2025年08月02日 09:39:12 周六 上午
2023-11-11T11:11:11
```

### 4. Duration时间间隔 (Test_Duration)

```java
import java.time.Duration;
import java.time.LocalDateTime;

public class Test_Duration {
    public static void main(String[] args) {
        LocalDateTime start = LocalDateTime.of(2025, 1, 1, 8, 10, 30);
        LocalDateTime end = LocalDateTime.of(2025, 1, 1, 8, 14, 6);

        // 计算两个时间的间隔
        Duration duration = Duration.between(start, end);

        System.out.println(duration.toDays());    // 间隔多少天
        System.out.println(duration.toHours());   // 间隔多少小时
        System.out.println(duration.toMinutes()); // 间隔多少分钟
        System.out.println(duration.toMillis());  // 间隔多少毫秒
    }
}
```

**运行结果**：
```
516
12398
743900
44634034388
```

### 5. Period日期间隔 (Test_Period)

```java
import java.time.LocalDate;
import java.time.Period;

public class Test_Period {
    public static void main(String[] args) {
        LocalDate start = LocalDate.of(2001, 1, 1);
        LocalDate end = LocalDate.of(2025, 12, 31);

        // 计算两个日期的间隔
        Period period = Period.between(start, end);
        System.out.println(period.getYears()); // 24
    }
}
```

**运行结果**：
```
24
```

### 6. Instant时间戳 (InstantTest)

```java
import java.time.Instant;

public class InstantTest {
    public static void main(String[] args) {
        // 获取当前时间戳
        Instant now = Instant.now();
        System.out.println(now); // 2025-08-02T01:39:49.332554400Z

        // 获取从1970-01-01T00:00:00Z开始的秒数
        long epochSecond = now.getEpochSecond();
        System.out.println(epochSecond); // 1754098789

        // 获取纳秒部分
        int nano = now.getNano();
        System.out.println(nano); // 332554400
    }
}
```

**运行结果**：
```
2025-08-02T01:39:49.332554400Z
1754098789
332554400
```

### 7. 时区处理 (ZoneId_id)

```java
import java.time.ZoneId;
import java.time.ZonedDateTime;

public class ZoneId_id {
    public static void main(String[] args) {
        // 获取系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();
        System.out.println(zoneId);

        // 获取所有可用时区（部分展示）
        ZoneId.getAvailableZoneIds().stream()
            .limit(10)
            .forEach(System.out::println);

        // 获取指定时区的当前时间
        ZonedDateTime now1 = ZonedDateTime.now(ZoneId.of("America/New_York"));
        ZonedDateTime now2 = ZonedDateTime.now(ZoneId.of("UTC"));

        System.out.println(now1);
        System.out.println(now2);
    }
}
```

**运行结果**（部分）：
```
Europe/Vatican
Asia/Amman
Etc/UTC
SystemV/AST4ADT
Asia/Tokyo
America/Toronto
Asia/Singapore
Australia/Lindeman
America/Los_Angeles
SystemV/EST5EDT
2025-08-01T21:40:02.*********-04:00[America/New_York]
2025-08-02T01:40:02.310448500Z[UTC]
```

## 💡 实践应用总结

### StringBuilder适用场景
- **字符串频繁拼接**：循环中的字符串构建
- **动态内容生成**：HTML、SQL语句构建
- **性能敏感场景**：避免String拼接的性能损耗

### StringJoiner适用场景
- **数组/集合元素连接**：用指定分隔符连接元素
- **格式化输出**：需要前缀后缀的字符串拼接
- **简化代码**：替代复杂的StringBuilder逻辑

### Math适用场景
- **数值计算**：基本数学运算
- **数据处理**：取整、绝对值等操作
- **算法实现**：数学公式计算

### BigDecimal适用场景
- **金融计算**：货币金额精确计算
- **商业系统**：价格、税费等精确运算
- **科学计算**：需要高精度的小数运算

### JDK8时间API适用场景
- **日期时间处理**：替代旧的Date和Calendar
- **时间计算**：日期间隔、时间差计算
- **格式化解析**：日期时间的格式转换
- **时区处理**：跨时区的时间操作

## 🎯 学习检验

通过day04的学习，您应该能够：
1. 熟练使用StringBuilder进行高效字符串操作
2. 掌握StringJoiner的便捷字符串连接功能
3. 运用Math类进行基本数学运算
4. 使用BigDecimal解决浮点数精度问题
5. 掌握JDK8时间API的核心功能和应用场景

这些常用API是Java开发的基础工具，熟练掌握它们将大大提高开发效率和代码质量。

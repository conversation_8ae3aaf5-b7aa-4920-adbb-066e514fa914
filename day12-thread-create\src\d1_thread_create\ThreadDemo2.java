package d1_thread_create;

public class ThreadDemo2 {
    public static void main(String[] args) {
        // 多线程创建方式二：实现Runnable接口

        //3.创建任务对象
        MyRunnable target = new MyRunnable();
        //4.任务对象交给线程对象
        Thread t = new Thread(target);

        //5.启动线程
        t.start();

        for (int i = 0; i < 5; i++) {
            System.out.println("主线程输出：" + i);
        }
    }
}

//1.定义一个任务类   实现runnable接口
class MyRunnable implements Runnable{
    //2.重写run方法
    @Override
    public void run() {
        for (int i = 0; i < 5; i++) {
            System.out.println("子线程输出：" + i);
        }
    }
}